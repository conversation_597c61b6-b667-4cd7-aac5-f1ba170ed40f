# syntax=docker/dockerfile:experimental

# BUILD
FROM public.ecr.aws/z5y1f1y8/gradle:7.4.2-jdk11 as builder
LABEL maintainer="<PERSON><PERSON><PERSON>, <EMAIL>"

COPY build.gradle gradle.properties settings.gradle /app/
WORKDIR /app



COPY . .
RUN --mount=type=cache,sharing=private,id=gradle,target=/root/.gradle \
    --mount=type=cache,sharing=private,id=gradle,target=/home/<USER>/.gradle \
    gradle clean build -x test
#    gradle build -x test -i --stacktrace

# RUN
FROM flink:1.20-java11

# Download and install the Flink S3 filesystem plugin for S3 checkpointing
RUN mkdir -p /opt/flink/plugins/s3-fs-hadoop \
    && wget -O /opt/flink/plugins/s3-fs-hadoop/flink-s3-fs-hadoop-1.20.0.jar https://repo1.maven.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.20.0/flink-s3-fs-hadoop-1.20.0.jar

# Download and add AWS Java SDK bundle required for S3 plugin
RUN wget -O /opt/flink/plugins/s3-fs-hadoop/aws-java-sdk-bundle-1.11.1024.jar https://repo1.maven.org/maven2/com/amazonaws/aws-java-sdk-bundle/1.11.1024/aws-java-sdk-bundle-1.11.1024.jar

# Download and add Hadoop AWS JAR for S3A filesystem support
RUN wget -O /opt/flink/plugins/s3-fs-hadoop/hadoop-aws-3.3.1.jar https://repo1.maven.org/maven2/org/apache/hadoop/hadoop-aws/3.3.1/hadoop-aws-3.3.1.jar

# (Optional) The container must have AWS credentials via environment or IAM role for S3 access

# Copy the built JAR from the builder stage
RUN mkdir -p /opt/flink/usrlib

COPY --from=builder /app/build/libs/fraud-lens-jobs-0.1.0.jar /opt/flink/usrlib/fraud-lens-jobs.jar

# Set working directory
WORKDIR /opt/flink