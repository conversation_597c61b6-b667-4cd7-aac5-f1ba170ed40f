# fraud-lens-jobs

**fraud-lens-jobs** is a Flink-based project providing real-time streaming jobs for FraudLens, a fraud detection system. It processes high-volume transaction data to identify suspicious patterns with low latency. The project uses Spring for modular configuration and dependency injection, but it is **not a Spring Boot application**. Flink jobs are packaged as plain Java JARs deployable directly to Flink clusters.

> **Note:** This project uses the core Spring Framework for dependency injection and configuration, but does **not** use Spring Boot. There is no embedded server or `bootRun` support; jobs are packaged as plain Java JARs for execution with Flink.

---

## 📦 Features

- **Real-time processing** of transactional data streams using Apache Flink.
- **Spring-based configuration** for modular, maintainable, and testable job components.
- **Graceful shutdown** support with configurable timeouts and signal handling.
- Flexible architecture for adding new fraud detection event sources.
- **Separate jobs for order and order refund events** with independent configuration and topics.
- **Identical aggregation flows for order and refund jobs:**
  - User-level aggregation (sliding window)
  - Pincode-level aggregation (sliding window)
  - Payment method-level aggregation (sliding window)
- Packaged as standard JARs for deployment via `flink run`.

---

## 🚀 Getting Started

### Prerequisites

- JDK 11 installed
- Gradle 7.4.2 installed (or use the included Gradle wrapper)
- Access to a Flink cluster or standalone Flink runtime for testing

### Build the project

```bash
./gradlew clean build
```

### Run the application

Use the Flink CLI to run the job. Replace `<main-class>` with your job's main class (e.g., `com.lenskart.platform.fraudcontrol.fraudlensjobs.order.jobs.launcher.OrderEventAggregationJobLauncher` or `com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.jobs.launcher.OrderRefundEventAggregationJobLauncher`) and `<jar-file>` with the path to your built JAR (e.g., `build/libs/fraud-lens-jobs-0.1.0.jar`):

```bash
flink run -c com.lenskart.platform.fraudcontrol.fraudlensjobs.order.jobs.launcher.OrderEventAggregationJobLauncher build/libs/fraud-lens-jobs-0.1.0.jar
```

**OrderRefund Example:**
```bash
flink run -c com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.jobs.launcher.OrderRefundEventAggregationJobLauncher build/libs/fraud-lens-jobs-0.1.0.jar
```

---

## 📂 Directory Structure

```
src/main/java/com/lenskart/platform/fraudcontrol/fraudlensjobs/
  jobs/
    impl/
      order/
        OrderEventAggregationJob.java
        OrderPincodeAggregationJob.java
        OrderPaymentMethodAggregationJob.java
      orderrefund/
        OrderRefundEventAggregationJob.java
        OrderRefundPincodeAggregationJob.java
        OrderRefundPaymentMethodAggregationJob.java
    launcher/
      order/
        OrderEventAggregationJobLauncher.java
        OrderPincodeAggregationJobLauncher.java
        OrderPaymentMethodAggregationJobLauncher.java
      orderrefund/
        OrderRefundEventAggregationJobLauncher.java
        OrderRefundPincodeAggregationJobLauncher.java
        OrderRefundPaymentMethodAggregationJobLauncher.java
  events/
    dto/
      order/
        ...
      orderrefund/
        ...
    richwindowfunctions/
      ...
```

---

## 📋 Configuration

### Application Properties
```properties
# Order Event Aggregation
order.user.aggregation.window.size.minutes=5
order.user.aggregation.window.slide.minutes=1
order.pincode.aggregation.window.size.minutes=10
order.pincode.aggregation.window.slide.minutes=2
order.paymentmethod.aggregation.window.size.minutes=15
order.paymentmethod.aggregation.window.slide.minutes=5

# Order Refund Event Aggregation
orderrefund.user.aggregation.window.size.minutes=5
orderrefund.user.aggregation.window.slide.minutes=1
orderrefund.pincode.aggregation.window.size.minutes=10
orderrefund.pincode.aggregation.window.slide.minutes=2
orderrefund.paymentmethod.aggregation.window.size.minutes=15
orderrefund.paymentmethod.aggregation.window.slide.minutes=5

# Kafka Configuration
kafka.bootstrap-servers=localhost:9092
kafka.order-events-topic=order-events
kafka.order-aggregates-topic=order-aggregates
kafka.order-refund-events-topic=order-refund-events
kafka.order-aggregator-group-id=order-event-aggregator
kafka.orderrefund-aggregator-group-id=order-refund-event-aggregator

# Flink Configuration
flink.checkpoint.interval=10000
flink.shutdown.timeout.ms=30000
flink.order.aggregation.user.parallelism=1
flink.order.aggregation.pincode.parallelism=1
flink.order.aggregation.paymentmethod.parallelism=1
flink.orderrefund.aggregation.user.parallelism=1
flink.orderrefund.aggregation.pincode.parallelism=1
flink.orderrefund.aggregation.paymentmethod.parallelism=1
```

---

## 🤹 Job Types

### Order Event Jobs
- **OrderEventAggregationJob**: Aggregates order events with user-level (sliding window), pincode, and payment method branches.
- **OrderPincodeAggregationJob**: Windowed aggregation by pincode.
- **OrderPaymentMethodAggregationJob**: Windowed aggregation by payment method.

### Order Refund Event Jobs
- **OrderRefundEventAggregationJob**: Aggregates order refund events with user-level (sliding window), pincode, and payment method branches. The flow is now functionally identical to order jobs, with only event types and topics differing.
- **OrderRefundPincodeAggregationJob**: Windowed aggregation by pincode for refund events.
- **OrderRefundPaymentMethodAggregationJob**: Windowed aggregation by payment method for refund events.

All refund jobs now support user-level aggregation and have the same windowing and output structure as order jobs, with their own DTOs, window functions, configuration, and Kafka topics.

---

## 🧪 Testing

Run the test suite:
```bash
./gradlew test
```

Run specific test:
```bash
./gradlew test --tests ShutdownHandlerTest
```

---

## 📦 Deployment

### Local Development
You can run the job locally using the Flink CLI as described above. There is no Spring Boot or `bootRun` support.

### Production Deployment
```bash
# Build the application
./gradlew clean build

# Deploy to Flink cluster
flink run -c com.lenskart.platform.fraudcontrol.fraudlensjobs.order.jobs.launcher.OrderEventAggregationJobLauncher build/libs/fraud-lens-jobs-0.1.0.jar
```

---

## 🔍 Monitoring

The application provides comprehensive logging for monitoring:

- **Startup**: Job initialization and configuration
- **Runtime**: Processing metrics and error handling
- **Shutdown**: Graceful shutdown progress and cleanup

### Log Levels
```properties
logging.level.root=INFO
logging.level.com.lenskart.platform.fraudcontrol.fraudlensjobs=DEBUG
```

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

---

## 📄 License

This project is licensed under the Apache License 2.0.

---

## ☁️ S3 Checkpointing Requirements

If you use S3 for Flink state checkpoints (as configured in `application.properties`):

- The Flink cluster (or Docker image) must include the S3 filesystem plugin (`flink-s3-fs-hadoop` for Flink 1.20).
- The plugin JAR must be placed in its own subdirectory under `/opt/flink/plugins/` (see Dockerfile for example).
- The container or environment must provide AWS credentials (via environment variables, IAM role, or credentials file) with access to the S3 bucket.
- For local development, you can override the checkpoint directory to use a local path (see `application.properties`).

For more details, see the [Flink documentation on filesystem plugins](https://nightlies.apache.org/flink/flink-docs-stable/docs/deployment/filesystems/plugins/).
