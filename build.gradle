plugins {
    id('java')
    id("maven-publish")
    id("jacoco")
    id("org.sonarqube") version "${sonarqubeVersion}"
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(11)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url 'https://repo.spring.io/libs-release/' }
    maven {
        url "http://archiva-new.prod.internal:8080/repository/internal/"
        allowInsecureProtocol = true
    }
}

dependencies {
    implementation "org.apache.flink:flink-connector-base:1.20.0"
    implementation "org.apache.flink:flink-streaming-java:1.20.0"
    implementation("org.apache.flink:flink-connector-kafka:3.4.0-1.20")
    implementation 'org.apache.flink:flink-statebackend-rocksdb:1.20.0'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.17.0'

    // Spring
    implementation("org.springframework:spring-context:5.3.38")
    implementation 'org.springframework:spring-core:5.3.36'

    implementation 'com.ecwid.consul:consul-api:1.4.5'

    implementation 'javax.annotation:javax.annotation-api:1.3.2'

    // Logging
    implementation("org.slf4j:slf4j-simple:2.0.12")

    // Testing
    testImplementation("org.junit.jupiter:junit-jupiter:5.9.3")

    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'


}

test {
    useJUnitPlatform()
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.enabled true
        html.enabled true
        csv.enabled true
    }
}

sonarqube {
    properties {
        property "sonar.projectKey", "${artifactName}"
        property "sonar.qualitygate.wait", true
    }
}



jar {
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}