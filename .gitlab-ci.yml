sonarqube-check:
  image: public.ecr.aws/z5y1f1y8/gradle:7.4.2-jdk11
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: gradle jacocoTestReport sonar -ParchivaUser=dummy -ParchivaPassword=dummy
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == 'master'

