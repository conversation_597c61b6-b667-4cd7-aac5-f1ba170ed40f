consul.enabled=${CONSULENABLED:true}
consul.host=${CONSULHOST:127.0.0.1}
consul.port=${CONSULPORT:8500}
consul.keyPrefix=${CONSULPREFIX:lk-platform-config}/fraud-lens-jobs,${PROFILE:dev}/v1
consul.aclToken=${ACL_TOKEN:dummyToken}

app.name=fraud-lens-jobs

#Order Event
order.user.aggregation.window.size.minutes=5
order.user.aggregation.window.slide.minutes=1
order.event.aggregation.source.name=fraud-lens-order-events

#kafka
kafka.bootstrap-servers=localhost:9092
kafka.order-events-topic=fraud-lens-order-events
kafka.order-aggregator-group-id=fraud-lens-order-event-aggregator
kafka.order-user-aggregates-topic=fraud-lens-order-aggregates
kafka.order-pincode-topic=fraud-lens-order-pincode-event
kafka.order-paymentmethod-topic=fraud-lens-order-paymentmethod-event


flink.order.aggregation.user.parallelism=1
flink.order.aggregation.pincode.parallelism=1
flink.order.aggregation.paymentmethod.parallelism=1

# Flink environment configuration
#flink.execution.mode=REMOTE
#flink.jobmanager.address=localhost
#flink.jobmanager.port=6123
flink.state.backend=${FLINK_STATE_BACKEND:rocksdb}
#flink.state.checkpoints.dir=file:///Users/<USER>/Documents/Flink/flink-checkpoints
flink.state.checkpoints.dir=${FLINK_STATE_CHECKPOINTS_DIR:s3://flink-data-preprod/checkpoints/lk-platform-fraud-lens-jobs-test}
# Time interval between state checkpoints in milliseconds
flink.checkpoint.interval=10000

# Graceful shutdown configuration
# Timeout for graceful shutdown in milliseconds (default: 30 seconds)
flink.shutdown.timeout.ms=30000

# Logging configuration
logging.level.root=INFO
logging.level.com.lenskart.platform.fraudcontrol.fraudlensjobs=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Payment Method Aggregation Hourly/48hr
order.paymentmethod.aggregation.window.size.long.duration=5
order.paymentmethod.aggregation.window.slide.long.duration=1
order.paymentmethod.aggregation.window.size.short.duration=5
order.paymentmethod.aggregation.window.slide.short.duration=1

# Pincode Aggregation Hourly/48hr
order.pincode.aggregation.window.size.short.duration=5
order.pincode.aggregation.window.slide.short.duration=1
order.pincode.aggregation.window.size.long.duration=5
order.pincode.aggregation.window.slide.long.duration=1

# Kafka output topics for aggregations
kafka.order-pincode-aggregates-hourly-topic=fraud-lens-order-pincode-aggregates
kafka.order-paymentmethod-aggregates-hourly-topic=fraud-lens-order-paymentmethod-aggregates

# Kafka group IDs and source names
order.event.payment-method-aggregation.source.name=fraud-lens-order-payment-method-aggregation-source
order.pincode.aggregation.source.name=fraud-lens-order-pincode-aggregation-source
kafka.order.pincode-aggregator-group-id=fraud-lens-order-pincode-event-aggregator
kafka.order.payment-method-aggregator-group-id=fraud-lens-order-payment-method-event-aggregator


# Order Refund Event
orderrefund.user.aggregation.window.size.minutes=5
orderrefund.user.aggregation.window.slide.minutes=1
orderrefund.event.aggregation.source.name=fraud-lens-order-refund-events

# Kafka topics for refund events
kafka.orderrefund-events-topic=fraud-lens-order-refund-events
kafka.orderrefund-aggregator-group-id=fraud-lens-order-refund-event-aggregator
kafka.orderrefund-user-aggregates-topic=fraud-lens-order-refund-aggregates
kafka.orderrefund-pincode-topic=fraud-lens-order-refund-pincode-event
kafka.orderrefund-paymentmethod-topic=fraud-lens-order-refund-paymentmethod-event

flink.orderrefund.aggregation.user.parallelism=1
flink.orderrefund.aggregation.pincode.parallelism=1
flink.orderrefund.aggregation.paymentmethod.parallelism=1

# Payment Method Aggregation Hourly/48hr for refunds
orderrefund.paymentmethod.aggregation.window.size.long.duration=5
orderrefund.paymentmethod.aggregation.window.slide.long.duration=1
orderrefund.paymentmethod.aggregation.window.size.short.duration=5
orderrefund.paymentmethod.aggregation.window.slide.short.duration=1

# Pincode Aggregation Hourly/48hr for refunds
orderrefund.pincode.aggregation.window.size.short.duration=5
orderrefund.pincode.aggregation.window.slide.short.duration=1
orderrefund.pincode.aggregation.window.size.long.duration=5
orderrefund.pincode.aggregation.window.slide.long.duration=1

# Kafka output topics for refund aggregations
kafka.orderrefund-pincode-aggregates-hourly-topic=fraud-lens-order-refund-pincode-aggregates
kafka.orderrefund-paymentmethod-aggregates-hourly-topic=fraud-lens-order-refund-paymentmethod-aggregates

# Kafka group IDs and source names for refunds
orderrefund.event.payment-method-aggregation.source.name=fraud-lens-order-refund-payment-method-aggregation-source
orderrefund.pincode.aggregation.source.name=fraud-lens-order-refund-pincode-aggregation-source
kafka.orderrefund.pincode-aggregator-group-id=fraud-lens-order-refund-pincode-event-aggregator
kafka.orderrefund.payment-method-aggregator-group-id=fraud-lens-order-refund-payment-method-event-aggregator



