package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.richwindowfunctions;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderEvent;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderPincodeAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.util.Collector;

public class PincodeWindowFunction implements WindowFunction<OrderEvent, OrderPincodeAggregation, String, TimeWindow> {
    @Override
    public void apply(String pincode, TimeWindow window, Iterable<OrderEvent> input, Collector<OrderPincodeAggregation> out) {
        double totalAmount = 0;
        long totalOrderCount = 0;
        for (OrderEvent event : input) {
            totalAmount += event.getAmount();
            totalOrderCount++;
        }
        out.collect(OrderPincodeAggregation.builder()
            .pincode(pincode)
            .totalAmount(totalAmount)
            .totalOrderCount(totalOrderCount)
            .windowStart(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getStart()).atZone(java.time.ZoneId.systemDefault())))
            .windowEnd(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getEnd()).atZone(java.time.ZoneId.systemDefault())))
            .build());
    }
} 