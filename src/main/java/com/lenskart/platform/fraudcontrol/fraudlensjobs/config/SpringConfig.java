package com.lenskart.platform.fraudcontrol.fraudlensjobs.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;

/**
 * Created by <PERSON><PERSON><PERSON> Kumar
 */


@Configuration
@PropertySource("classpath:application.properties")
@ComponentScan(basePackages = "com.lenskart.platform.fraudcontrol.fraudlensjobs")
public class SpringConfig {

    private static final Logger log = LoggerFactory.getLogger(SpringConfig.class);

    @Bean
    public static PropertySourcesPlaceholderConfigurer placeholderConfigurer() {
        log.info("Creating PropertySourcesPlaceholderConfigurer bean");
        return new PropertySourcesPlaceholderConfigurer();
    }
}