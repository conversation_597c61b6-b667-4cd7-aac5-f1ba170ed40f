package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPaymentMethodAggregation implements Serializable {
    private String paymentMethod;
    private double totalAmount;
    private long totalOrderCount;
    private String windowStart;
    private String windowEnd;
} 