package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.jobs.impl;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.deserializer.MessageRequestDeserializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.Message;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.MessageRequest;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.serializer.MessageRequestSerializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundEvent;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPaymentMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPincode;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundValueAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.RefundMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.richwindowfunctions.OrderRefundUserValueWindowFunction;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.configuration.CheckpointingOptions;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.StateBackendOptions;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Flink job for aggregating order refund events for fraud detection.
 *
 * <p>This job consumes order refund events from Kafka, deduplicates them by customerId (or refundId if available), and then:
 * <ul>
 *   <li>Performs user-level aggregation (sliding window) by customerId, outputting to a Kafka topic.</li>
 *   <li>Forwards each refund event by pincode to a Kafka topic (no aggregation).</li>
 *   <li>Forwards each refund event by payment method to a Kafka topic (no aggregation).</li>
 * </ul>
 *
 * <p>The flow, windowing, and output structure are functionally identical to OrderEventAggregationJob, with only the event/aggregation types and Kafka topics differing.
 *
 * <p>All refund event jobs, DTOs, and configuration are kept separate from order event jobs.
 *
 * Created by Neeraj Kumar
 */
@Component(FraudLensJobsConstants.ORDER_REFUND)
public class OrderRefundEventAggregationJob implements FlinkJob {
    private static final Logger log = LoggerFactory.getLogger(OrderRefundEventAggregationJob.class);

    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicReference<StreamExecutionEnvironment> executionEnvironmentRef = new AtomicReference<>();
    private final AtomicReference<CompletableFuture<Void>> jobExecutionFuture = new AtomicReference<>();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "OrderRefundEventAggregationJob-Executor");
        t.setDaemon(false);
        return t;
    });
    private final String jobName = "Order Refund Event Aggregation";

    @Value("${orderrefund.user.aggregation.window.size.minutes}")
    public int refundUserAggregationWindowSize;

    @Value("${orderrefund.user.aggregation.window.slide.minutes}")
    public int refundUserAggregationWindowSlideSize;

    @Value("${kafka.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.orderrefund-events-topic}")
    public String refundKafkaOrderRefundEventsTopic;

    @Value("${kafka.orderrefund-user-aggregates-topic}")
    public String refundKafkaOrderRefundUserAggregatesTopic;

    @Value("${kafka.orderrefund-pincode-topic}")
    public String refundKafkaOrderRefundPincodeTopic;

    @Value("${kafka.orderrefund-paymentmethod-topic}")
    public String refundKafkaOrderRefundPaymentMethodTopic;

    @Value("${kafka.orderrefund-aggregator-group-id}")
    public String kafkaOrderRefundAggregatorGroupId;

    @Value("${orderrefund.event.aggregation.source.name}")
    public String orderRefundEventAggregationSourceName;

    @Value("${flink.checkpoint.interval}")
    public long checkpointInterval;

    @Value("${flink.state.backend}")
    public String flinkStateBackend;

    @Value("${flink.state.checkpoints.dir}")
    public String flinkCheckpointsDir;

    @Value("${flink.orderrefund.aggregation.user.parallelism}")
    public int userAggregationParallelism;

    @Value("${flink.orderrefund.aggregation.pincode.parallelism}")
    public int pincodeAggregationParallelism;

    @Value("${flink.orderrefund.aggregation.paymentmethod.parallelism}")
    public int paymentMethodAggregationParallelism;

    @Value("${flink.shutdown.timeout.ms:30000}")
    public long shutdownTimeoutMs;

    private void executeJob() throws Exception {
        log.info("executeJob:Config: userWindowSize={} userWindowSlide={} kafkaBootstrapServers={} refundKafkaOrderRefundEventsTopic={} kafkaOrderRefundAggregatorGroupId={} sourceName={} checkpointInterval={}",
                refundUserAggregationWindowSize, refundUserAggregationWindowSlideSize,
                kafkaBootstrapServers, refundKafkaOrderRefundEventsTopic,
                kafkaOrderRefundAggregatorGroupId, orderRefundEventAggregationSourceName, checkpointInterval);
        Configuration config = new Configuration();
        config.set(StateBackendOptions.STATE_BACKEND, flinkStateBackend);
        config.set(CheckpointingOptions.CHECKPOINTS_DIRECTORY, flinkCheckpointsDir);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        executionEnvironmentRef.set(env);
        env.enableCheckpointing(checkpointInterval);

        // --- Source: Read refund events from Kafka ---
        KafkaSource<MessageRequest<OrderRefundEvent>> kafkaSource = KafkaSource.<MessageRequest<OrderRefundEvent>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics(refundKafkaOrderRefundEventsTopic)
                .setGroupId(kafkaOrderRefundAggregatorGroupId)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new MessageRequestDeserializationSchema<>(OrderRefundEvent.class))
                .build();

        DataStream<OrderRefundEvent> events = env.fromSource(
            kafkaSource,
            WatermarkStrategy.noWatermarks(),
            orderRefundEventAggregationSourceName
        )
        .map(OrderRefundEventAggregationJob::extractOrderRefundEvent)
        .filter(Objects::nonNull);

        // --- Deduplicate events by refundId ---
        DataStream<OrderRefundEvent> dedupedEvents = events
            .keyBy(OrderRefundEvent::getRefundId) // You may want to use refundId if available
            .process(new DeduplicateOrderRefundEventProcessFunction());

        // --- User-level aggregation (sliding window) ---
        // This branch is functionally identical to the user aggregation in OrderEventAggregationJob, but for refund events.
        dedupedEvents
            .keyBy(OrderRefundEvent::getCustomerId)
            .window(org.apache.flink.streaming.api.windowing.assigners.SlidingProcessingTimeWindows.of(
                org.apache.flink.streaming.api.windowing.time.Time.minutes(refundUserAggregationWindowSize),
                org.apache.flink.streaming.api.windowing.time.Time.minutes(refundUserAggregationWindowSlideSize)))
            .apply(new OrderRefundUserValueWindowFunction())
            .returns(OrderRefundValueAggregation.class)
            .map(new OrderRefundUserAggregationMessageMapper(refundKafkaOrderRefundUserAggregatesTopic))
            .sinkTo(KafkaSink.<MessageRequest<OrderRefundValueAggregation>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                    KafkaRecordSerializationSchema.<MessageRequest<OrderRefundValueAggregation>>builder()
                        .setTopic(refundKafkaOrderRefundUserAggregatesTopic)
                        .setKeySerializationSchema(msgReq -> msgReq.getKey() != null ? msgReq.getKey().getBytes(java.nio.charset.StandardCharsets.UTF_8) : null)
                        .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                        .build())
                .build())
            .setParallelism(userAggregationParallelism);

        // --- Kafka sinks for each output branch ---
        KafkaSink<MessageRequest<OrderRefundPincode>> pincodeSink = KafkaSink.<MessageRequest<OrderRefundPincode>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderRefundPincode>>builder()
                                .setTopic(refundKafkaOrderRefundPincodeTopic)
                                .setKeySerializationSchema(msgReq -> {
                                    String pincode = msgReq.getMessage().getData().getPincode();
                                    return pincode != null ? pincode.getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();

        KafkaSink<MessageRequest<OrderRefundPaymentMethod>> paymentMethodSink = KafkaSink.<MessageRequest<OrderRefundPaymentMethod>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderRefundPaymentMethod>>builder()
                                .setTopic(refundKafkaOrderRefundPaymentMethodTopic)
                                .setKeySerializationSchema(msgReq -> {
                                    String paymentMethod = msgReq.getMessage().getData().getPaymentMethod();
                                    return paymentMethod != null ? paymentMethod.getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();

        // --- For each refund event, create and send a pincode event (no aggregation) ---
        // This branch is functionally identical to the pincode branch in OrderEventAggregationJob, but for refund events.
        dedupedEvents
            .keyBy(OrderRefundEvent::getPincode) // or getPincode if available
            .map(new OrderRefundPincodeMessageMapper())
            .sinkTo(pincodeSink)
            .setParallelism(pincodeAggregationParallelism);

        // --- For each refund event, create and send refund method events for each refund method with amount > 0 ---
        // This branch is functionally identical to the payment method branch in OrderEventAggregationJob, but for refund events.
        dedupedEvents
            .flatMap(new OrderRefundPaymentMethodFlatMapper())
            .sinkTo(paymentMethodSink)
            .setParallelism(paymentMethodAggregationParallelism);

        env.execute(jobName);
    }

    private static OrderRefundEvent extractOrderRefundEvent(MessageRequest<OrderRefundEvent> value) {
        return value != null ? value.getMessage().getData() : null;
    }

    private static MessageRequest<OrderRefundPincode> toOrderRefundPincodeMessage(OrderRefundEvent refundEvent) {
        OrderRefundPincode pincode = OrderRefundPincode.builder()
                .refundId(refundEvent.getRefundId())
                .customerId(refundEvent.getCustomerId())
                .pincode(refundEvent.getPincode())
                .amount(refundEvent.getAmount())
                .createdAt(new java.util.Date())
                .build();
        return MessageRequest.<OrderRefundPincode>builder()
                .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_PINCODE)
                .message(Message.<OrderRefundPincode>builder().data(pincode).build())
                .build();
    }



    private static class DeduplicateOrderRefundEventProcessFunction extends org.apache.flink.streaming.api.functions.KeyedProcessFunction<Long, OrderRefundEvent, OrderRefundEvent> {
        private transient org.apache.flink.api.common.state.ValueState<Boolean> seen;
        @Override
        public void open(Configuration parameters) {
            org.apache.flink.api.common.state.ValueStateDescriptor<Boolean> desc = new org.apache.flink.api.common.state.ValueStateDescriptor<>("seen", Boolean.class);
            seen = getRuntimeContext().getState(desc);
        }
        @Override
        public void processElement(OrderRefundEvent value, Context ctx, Collector<OrderRefundEvent> out) throws Exception {
            if (seen.value() == null) {
                seen.update(true);
                out.collect(value);
            }
        }
    }

    public static class OrderRefundUserAggregationMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderRefundValueAggregation, MessageRequest<OrderRefundValueAggregation>>, java.io.Serializable {
        private final String topic;

        public OrderRefundUserAggregationMessageMapper(String topic) {
            this.topic = topic;
        }

        @Override
        public MessageRequest<OrderRefundValueAggregation> map(OrderRefundValueAggregation agg) {
            return MessageRequest.<OrderRefundValueAggregation>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_USER_TOTAL_VALUE)
                    .message(Message.<OrderRefundValueAggregation>builder().data(agg).build())
                    .key(String.valueOf(agg.getCustomerId()))
                    .topic(topic)
                    .build();
        }
    }

    @Override
    public void launch() {
        isRunning.set(true);
        jobExecutionFuture.set(CompletableFuture.runAsync(() -> {
            try {
                executeJob();
            } catch (Exception e) {
                log.error("Error running job", e);
            }
        }, executorService));
        setupShutdownHook();
    }

    private void setupShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutdown hook triggered for {}", jobName);
            try {
                shutdown(shutdownTimeoutMs);
            } catch (Exception e) {
                log.error("Error during shutdown", e);
            }
        }));
    }

    @Override
    public void shutdown(long timeoutMs) throws Exception {
        isRunning.set(false);
        executorService.shutdown();
        executorService.awaitTermination(timeoutMs, TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean isRunning() {
        return isRunning.get();
    }

    @Override
    public String getJobName() {
        return jobName;
    }

    @Override
    public StreamExecutionEnvironment getExecutionEnvironment() {
        return executionEnvironmentRef.get();
    }

    @PreDestroy
    public void cleanup() {
        try {
            shutdown(shutdownTimeoutMs);
        } catch (Exception e) {
            log.error("Error during cleanup", e);
        }
    }

    public static class OrderRefundPincodeMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderRefundEvent, MessageRequest<OrderRefundPincode>>, java.io.Serializable {
        @Override
        public MessageRequest<OrderRefundPincode> map(OrderRefundEvent refundEvent) {
            OrderRefundPincode pincode = OrderRefundPincode.builder()
                    .refundId(refundEvent.getRefundId())
                    .customerId(refundEvent.getCustomerId())
                    .pincode(refundEvent.getPincode())
                    .amount(refundEvent.getAmount())
                    .createdAt(new java.util.Date())
                    .build();
            return MessageRequest.<OrderRefundPincode>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_PINCODE)
                    .message(Message.<OrderRefundPincode>builder().data(pincode).build())
                    .build();
        }
    }

    public static class OrderRefundPaymentMethodFlatMapper implements org.apache.flink.api.common.functions.FlatMapFunction<OrderRefundEvent, MessageRequest<OrderRefundPaymentMethod>>, java.io.Serializable {
        @Override
        public void flatMap(OrderRefundEvent refundEvent, org.apache.flink.util.Collector<MessageRequest<OrderRefundPaymentMethod>> out) throws Exception {
            // Create events for each refund method with amount > 0
            if (refundEvent.getRefundMethods() != null) {
                for (RefundMethod refundMethod : refundEvent.getRefundMethods()) {
                    if (refundMethod.getAmount() > 0) {
                        OrderRefundPaymentMethod orderRefundPaymentMethod = OrderRefundPaymentMethod.builder()
                                .refundId(refundEvent.getRefundId())
                                .customerId(refundEvent.getCustomerId())
                                .paymentMethod(refundMethod.getMethod())
                                .amount(refundMethod.getAmount())
                                .createdAt(new java.util.Date())
                                .build();
                        
                        MessageRequest<OrderRefundPaymentMethod> messageRequest = MessageRequest.<OrderRefundPaymentMethod>builder()
                                .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_PAYMENT_METHOD)
                                .message(Message.<OrderRefundPaymentMethod>builder().data(orderRefundPaymentMethod).build())
                                .key(orderRefundPaymentMethod.getPaymentMethod())
                                .build();
                        
                        out.collect(messageRequest);
                    }
                }
            }
        }
    }
}