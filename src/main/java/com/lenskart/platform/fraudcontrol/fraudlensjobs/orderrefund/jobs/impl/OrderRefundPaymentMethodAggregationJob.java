package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.jobs.impl;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.Message;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.deserializer.MessageRequestDeserializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.MessageRequest;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPaymentMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPaymentMethodAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.richwindowfunctions.OrderRefundPaymentMethodWindowFunction;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.serializer.MessageRequestSerializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.configuration.CheckpointingOptions;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.StateBackendOptions;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.SlidingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Flink job for aggregating order refund events by payment method using sliding windows.
 *
 * <p>This job consumes refund payment method events from Kafka and performs windowed aggregation by payment method, outputting to a Kafka topic.
 *
 * <p>All refund event jobs, DTOs, and configuration are kept separate from order event jobs.
 *
 * Created by Neeraj Kumar
 */
@Component(FraudLensJobsConstants.ORDER_REFUND_PAYMENT_METHOD_AGGREGATION)
public class OrderRefundPaymentMethodAggregationJob implements FlinkJob {
    private static final Logger log = LoggerFactory.getLogger(OrderRefundPaymentMethodAggregationJob.class);

    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicReference<StreamExecutionEnvironment> executionEnvironmentRef = new AtomicReference<>();
    private final AtomicReference<CompletableFuture<Void>> jobExecutionFuture = new AtomicReference<>();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "OrderRefundPaymentMethodAggregationJob-Executor");
        t.setDaemon(false);
        return t;
    });
    private final String jobName = "Order Refund Payment Method Aggregation with Sliding Window";

    @Value("${orderrefund.paymentmethod.aggregation.window.size.short.duration}")
    public int windowSizeShortDuration;

    @Value("${orderrefund.paymentmethod.aggregation.window.slide.short.duration}")
    public int windowSlideShortDuration;

    @Value("${orderrefund.paymentmethod.aggregation.window.size.long.duration}")
    public int windowSizeLongDuration;

    @Value("${orderrefund.paymentmethod.aggregation.window.slide.long.duration}")
    public int windowSlideLongDuration;

    @Value("${kafka.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.orderrefund-paymentmethod-topic}")
    public String refundKafkaOrderRefundPaymentMethodTopic;

    @Value("${kafka.orderrefund-paymentmethod-aggregates-hourly-topic}")
    public String refundKafkaOrderRefundPaymentMethodAggregatesHourlyTopic;

    @Value("${kafka.orderrefund.payment-method-aggregator-group-id}")
    public String kafkaOrderRefundPaymentMethodAggregatorGroupId;

    @Value("${orderrefund.event.payment-method-aggregation.source.name}")
    public String orderRefundPaymentMethodAggregationSourceName;

    @Value("${flink.checkpoint.interval}")
    public long checkpointInterval;

    @Value("${flink.state.backend}")
    public String flinkStateBackend;

    @Value("${flink.state.checkpoints.dir}")
    public String flinkCheckpointsDir;

    @Value("${flink.orderrefund.aggregation.paymentmethod.parallelism}")
    public int paymentMethodAggregationParallelism;

    @Value("${flink.shutdown.timeout.ms:30000}")
    public long shutdownTimeoutMs;

    private void executeJob() throws Exception {
        log.info("executeJob:Config: windowSizeShortDuration={} windowSlideShortDuration={} kafkaBootstrapServers={} refundKafkaOrderRefundPaymentMethodTopic={} kafkaOrderRefundPaymentMethodAggregatorGroupId={} sourceName={} checkpointInterval={}",
                windowSizeShortDuration, windowSlideShortDuration,
                kafkaBootstrapServers, refundKafkaOrderRefundPaymentMethodTopic,
                kafkaOrderRefundPaymentMethodAggregatorGroupId, orderRefundPaymentMethodAggregationSourceName, checkpointInterval);
        Configuration config = new Configuration();
        config.set(StateBackendOptions.STATE_BACKEND, flinkStateBackend);
        config.set(CheckpointingOptions.CHECKPOINTS_DIRECTORY, flinkCheckpointsDir);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        executionEnvironmentRef.set(env);
        env.enableCheckpointing(checkpointInterval);

        // --- Source: Read refund payment method events from Kafka ---
        KafkaSource<MessageRequest<OrderRefundPaymentMethod>> kafkaSource = KafkaSource.<MessageRequest<OrderRefundPaymentMethod>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics(refundKafkaOrderRefundPaymentMethodTopic)
                .setGroupId(kafkaOrderRefundPaymentMethodAggregatorGroupId)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new MessageRequestDeserializationSchema<>(OrderRefundPaymentMethod.class))
                .build();

        DataStream<OrderRefundPaymentMethod> events = env.fromSource(
            kafkaSource,
            WatermarkStrategy.noWatermarks(),
            orderRefundPaymentMethodAggregationSourceName
        )
        .map(msgReq -> msgReq.getMessage().getData())
        .filter(Objects::nonNull);

        // --- Deduplicate events by composite key of refundId and paymentMethod ---
        DataStream<OrderRefundPaymentMethod> dedupedEvents = events
            .keyBy(event -> event.getRefundId() +  FraudLensJobsConstants.UNDER_SCORE + event.getPaymentMethod())
            .process(new DeduplicateOrderRefundPaymentMethodProcessFunction());

        // --- Short duration window aggregation ---
        DataStream<OrderRefundPaymentMethodAggregation> shortDurationAgg = dedupedEvents
            .keyBy(OrderRefundPaymentMethod::getPaymentMethod)
            .window(SlidingProcessingTimeWindows.of(
                Time.minutes(windowSizeShortDuration),
                Time.minutes(windowSlideShortDuration)))
            .apply(new OrderRefundPaymentMethodWindowFunction())
            .returns(new TypeHint<OrderRefundPaymentMethodAggregation>() {});

        // --- Long duration window aggregation ---
        DataStream<OrderRefundPaymentMethodAggregation> longDurationAgg = dedupedEvents
            .keyBy(OrderRefundPaymentMethod::getPaymentMethod)
            .window(SlidingProcessingTimeWindows.of(
                Time.minutes(windowSizeLongDuration),
                Time.minutes(windowSlideLongDuration)))
            .apply(new OrderRefundPaymentMethodWindowFunction())
            .returns(new TypeHint<OrderRefundPaymentMethodAggregation>() {});

        // --- Kafka sink for hourly aggregates ---
        KafkaSink<MessageRequest<OrderRefundPaymentMethodAggregation>> paymentMethodSink = KafkaSink.<MessageRequest<OrderRefundPaymentMethodAggregation>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderRefundPaymentMethodAggregation>>builder()
                                .setTopic(refundKafkaOrderRefundPaymentMethodAggregatesHourlyTopic)
                                .setKeySerializationSchema(msgReq -> {
                                    String paymentMethod = msgReq.getMessage().getData().getPaymentMethod();
                                    return paymentMethod != null ? paymentMethod.getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();

        shortDurationAgg
            .map(new OrderRefundPaymentMethodShortDurationMessageMapper())
            .sinkTo(paymentMethodSink)
            .setParallelism(paymentMethodAggregationParallelism);

        longDurationAgg
            .map(new OrderRefundPaymentMethodLongDurationMessageMapper())
            .sinkTo(paymentMethodSink)
            .setParallelism(paymentMethodAggregationParallelism);

        env.execute(jobName);
    }

    @Override
    public void launch() {
        isRunning.set(true);
        jobExecutionFuture.set(CompletableFuture.runAsync(() -> {
            try {
                executeJob();
            } catch (Exception e) {
                log.error("Error running job", e);
            }
        }, executorService));
        setupShutdownHook();
    }

    private void setupShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutdown hook triggered for {}", jobName);
            try {
                shutdown(shutdownTimeoutMs);
            } catch (Exception e) {
                log.error("Error during shutdown", e);
            }
        }));
    }

    @Override
    public void shutdown(long timeoutMs) throws Exception {
        isRunning.set(false);
        executorService.shutdown();
        executorService.awaitTermination(timeoutMs, TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean isRunning() {
        return isRunning.get();
    }

    @Override
    public String getJobName() {
        return jobName;
    }

    @Override
    public StreamExecutionEnvironment getExecutionEnvironment() {
        return executionEnvironmentRef.get();
    }

    @PreDestroy
    public void cleanup() {
        try {
            shutdown(shutdownTimeoutMs);
        } catch (Exception e) {
            log.error("Error during cleanup", e);
        }
    }

    public static class OrderRefundPaymentMethodShortDurationMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderRefundPaymentMethodAggregation, MessageRequest<OrderRefundPaymentMethodAggregation>>, java.io.Serializable {
        @Override
        public MessageRequest<OrderRefundPaymentMethodAggregation> map(OrderRefundPaymentMethodAggregation agg) {
            return MessageRequest.<OrderRefundPaymentMethodAggregation>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_PAYMENT_METHOD_SHORT_DURATION)
                    .message(Message.<OrderRefundPaymentMethodAggregation>builder().data(agg).build())
                    .build();
        }
    }

    public static class OrderRefundPaymentMethodLongDurationMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderRefundPaymentMethodAggregation, MessageRequest<OrderRefundPaymentMethodAggregation>>, java.io.Serializable {
        @Override
        public MessageRequest<OrderRefundPaymentMethodAggregation> map(OrderRefundPaymentMethodAggregation agg) {
            return MessageRequest.<OrderRefundPaymentMethodAggregation>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_PAYMENT_METHOD_LONG_DURATION)
                    .message(Message.<OrderRefundPaymentMethodAggregation>builder().data(agg).build())
                    .build();
        }
    }

    private static class DeduplicateOrderRefundPaymentMethodProcessFunction extends org.apache.flink.streaming.api.functions.KeyedProcessFunction<String, OrderRefundPaymentMethod, OrderRefundPaymentMethod> {
        private transient org.apache.flink.api.common.state.ValueState<Boolean> seen;
        
        @Override
        public void open(Configuration parameters) {
            org.apache.flink.api.common.state.ValueStateDescriptor<Boolean> desc = new org.apache.flink.api.common.state.ValueStateDescriptor<>("seen", Boolean.class);
            seen = getRuntimeContext().getState(desc);
        }
        
        @Override
        public void processElement(OrderRefundPaymentMethod value, Context ctx, Collector<OrderRefundPaymentMethod> out) throws Exception {
            if (seen.value() == null) {
                seen.update(true);
                out.collect(value);
            }
        }
    }
} 