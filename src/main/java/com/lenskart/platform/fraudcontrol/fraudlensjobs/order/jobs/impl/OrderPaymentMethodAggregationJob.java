package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.jobs.impl;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.deserializer.MessageRequestDeserializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.Message;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.MessageRequest;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderEvent;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderPaymentMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderPaymentMethodAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.richwindowfunctions.OrderPaymentMethodWindowFunction;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.serializer.MessageRequestSerializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.configuration.CheckpointingOptions;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.StateBackendOptions;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.api.windowing.assigners.SlidingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Component(FraudLensJobsConstants.ORDER_PAYMENT_METHOD_AGGREGATION)
public class OrderPaymentMethodAggregationJob implements FlinkJob {

    private static final Logger log = LoggerFactory.getLogger(OrderPaymentMethodAggregationJob.class);

    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicReference<StreamExecutionEnvironment> executionEnvironmentRef = new AtomicReference<>();
    private final AtomicReference<CompletableFuture<Void>> jobExecutionFuture = new AtomicReference<>();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "OrderPaymentMethodAggregationJob-Executor");
        t.setDaemon(false);
        return t;
    });
    private final String jobName = "Order Payment Method Aggregation with Sliding Window";

    @Value("${order.paymentmethod.aggregation.window.size.short.duration}")
    public int windowSizeShortDuration;

    @Value("${order.paymentmethod.aggregation.window.slide.short.duration}")
    public int windowSlideShortDuration;

    @Value("${order.paymentmethod.aggregation.window.size.long.duration}")
    public int windowSizeLongDuration;

    @Value("${order.paymentmethod.aggregation.window.slide.long.duration}")
    public int windowSlideLongDuration;

    @Value("${kafka.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.order-paymentmethod-topic}")
    public String orderKafkaOrderPaymentMethodTopic;

    @Value("${kafka.order.payment-method-aggregator-group-id}")
    public String kafkaOrderPaymentMethodAggregatorGroupId;

    @Value("${order.event.payment-method-aggregation.source.name}")
    public String orderPaymentMethodAggregationSourceName;

    @Value("${flink.checkpoint.interval}")
    public long checkpointInterval;

    @Value("${flink.state.backend}")
    public String flinkStateBackend;

    @Value("${flink.state.checkpoints.dir}")
    public String flinkCheckpointsDir;

    @Value("${flink.order.aggregation.paymentmethod.parallelism}")
    public int paymentMethodAggregationParallelism;

    @Value("${flink.shutdown.timeout.ms:30000}")
    public long shutdownTimeoutMs;

    @Value("${kafka.order-paymentmethod-aggregates-hourly-topic}")
    public String orderKafkaOrderPaymentMethodAggregatesHourlyTopic;

    @Override
    public void launch() {
        log.info("Starting OrderPaymentMethodAggregationJob");
        if (isRunning.compareAndSet(false, true)) {
            try {
                setupShutdownHook();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        executeJob();
                    } catch (Exception e) {
                        log.error("Job execution failed", e);
                        throw new RuntimeException(e);
                    }
                }, executorService);
                jobExecutionFuture.set(future);
                log.info("OrderPaymentMethodAggregationJob execution started");
            } catch (Exception e) {
                isRunning.set(false);
                throw e;
            }
        } else {
            log.warn("Job is already running");
        }
    }

    private void executeJob() {
        try {
            Configuration config = new Configuration();
            config.set(StateBackendOptions.STATE_BACKEND, flinkStateBackend);
            config.set(CheckpointingOptions.CHECKPOINTS_DIRECTORY, flinkCheckpointsDir);
            StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
            executionEnvironmentRef.set(env);
            env.enableCheckpointing(checkpointInterval);

            KafkaSource<MessageRequest<OrderPaymentMethod>> kafkaSource = KafkaSource.<MessageRequest<OrderPaymentMethod>>builder()
                    .setBootstrapServers(kafkaBootstrapServers)
                    .setTopics(orderKafkaOrderPaymentMethodTopic)
                    .setGroupId(kafkaOrderPaymentMethodAggregatorGroupId)
                    .setStartingOffsets(OffsetsInitializer.latest())
                    .setValueOnlyDeserializer(new MessageRequestDeserializationSchema<>(OrderPaymentMethod.class))
                    .build();

            DataStream<OrderPaymentMethod> events = env.fromSource(
                    kafkaSource,
                    WatermarkStrategy.noWatermarks(),
                    orderPaymentMethodAggregationSourceName
            )
            .map(value -> value != null && value.getMessage() != null ? value.getMessage().getData() : null)
            .filter(Objects::nonNull);

            DataStream<OrderPaymentMethod> dedupedEvents = events
                    .keyBy(event -> event.getOrderId() + FraudLensJobsConstants.UNDER_SCORE + event.getPaymentMethod())
                    .process(new DeduplicateOrderPaymentMethodProcessFunction());

            // Payment method window aggregation - 1hr
            dedupedEvents
                    .keyBy(OrderPaymentMethod::getPaymentMethod)
                    .window(SlidingProcessingTimeWindows.of(
                            Time.minutes(windowSizeShortDuration),
                            Time.minutes(windowSlideShortDuration)))
                    .apply(new OrderPaymentMethodWindowFunction())
                    .returns(new TypeHint<OrderPaymentMethodAggregation>() {})
                    .map(new PaymentMethodAggregationMessageMapper(FraudLensJobsConstants.EVENT_ORDER_PAYMENT_METHOD_SHORT_DURATION, orderKafkaOrderPaymentMethodAggregatesHourlyTopic))
                    .returns(new TypeHint<MessageRequest<OrderPaymentMethodAggregation>>() {})
                    .sinkTo(buildPaymentMethodSink(orderKafkaOrderPaymentMethodAggregatesHourlyTopic))
                    .setParallelism(paymentMethodAggregationParallelism);

            // Payment method window aggregation - 48hr
            dedupedEvents
                    .keyBy(OrderPaymentMethod::getPaymentMethod)
                    .window(SlidingProcessingTimeWindows.of(
                            Time.minutes(windowSizeLongDuration),
                            Time.minutes(windowSlideLongDuration)))
                    .apply(new OrderPaymentMethodWindowFunction())
                    .returns(new TypeHint<OrderPaymentMethodAggregation>() {})
                    .map(new PaymentMethodAggregationMessageMapper(FraudLensJobsConstants.EVENT_ORDER_PAYMENT_METHOD_LONG_DURATION, orderKafkaOrderPaymentMethodAggregatesHourlyTopic))
                    .returns(new TypeHint<MessageRequest<OrderPaymentMethodAggregation>>() {})
                    .sinkTo(buildPaymentMethodSink(orderKafkaOrderPaymentMethodAggregatesHourlyTopic))
                    .setParallelism(paymentMethodAggregationParallelism);

            env.execute(jobName);
        } catch (Exception e) {
            log.error("Error in executeJob", e);
            throw new RuntimeException(e);
        }
    }

    private static OrderEvent extractOrderEvent(MessageRequest<OrderEvent> value) {
        if (value != null && value.getMessage() != null) {
            return value.getMessage().getData();
        }
        return null;
    }

    private static MessageRequest<OrderPaymentMethodAggregation> toPaymentMethodAggregationMessage(OrderPaymentMethodAggregation agg, String eventType, String topic) {
        Message<OrderPaymentMethodAggregation> message = Message.<OrderPaymentMethodAggregation>builder()
                .data(agg)
                .build();
        return MessageRequest.<OrderPaymentMethodAggregation>builder()
                .type(eventType)
                .message(message)
                .key(agg.getPaymentMethod())
                .topic(topic)
                .build();
    }

    private KafkaSink<MessageRequest<OrderPaymentMethodAggregation>> buildPaymentMethodSink(String topic) {
        return KafkaSink.<MessageRequest<OrderPaymentMethodAggregation>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderPaymentMethodAggregation>>builder()
                                .setTopic(topic)
                                .setKeySerializationSchema(msgReq -> {
                                    String paymentMethod = msgReq.getMessage().getData().getPaymentMethod();
                                    return paymentMethod != null ? paymentMethod.getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();
    }

    private void setupShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutdown hook triggered for {}", jobName);
            try {
                shutdown(shutdownTimeoutMs);
            } catch (Exception e) {
                log.error("Error during shutdown hook execution", e);
            }
        }, "ShutdownHook-" + jobName));
    }

    @Override
    public void shutdown(long timeoutMs) throws Exception {
        log.info("Initiating graceful shutdown for {} with timeout {}ms", jobName, timeoutMs);
        if (isRunning.compareAndSet(true, false)) {
            try {
                CompletableFuture<Void> future = jobExecutionFuture.get();
                if (future != null && !future.isDone()) {
                    log.info("Cancelling job execution");
                    future.cancel(true);
                }
                log.info("Shutting down executor service");
                executorService.shutdown();
                if (!executorService.awaitTermination(timeoutMs, TimeUnit.MILLISECONDS)) {
                    log.warn("Executor service did not terminate within {}ms, forcing shutdown", timeoutMs);
                    executorService.shutdownNow();
                    if (!executorService.awaitTermination(5000, TimeUnit.MILLISECONDS)) {
                        log.error("Executor service did not terminate");
                    }
                }
                executionEnvironmentRef.set(null);
                jobExecutionFuture.set(null);
                log.info("Graceful shutdown completed for {}", jobName);
            } catch (Exception e) {
                log.error("Error during shutdown", e);
                throw e;
            }
        } else {
            log.info("Job {} is not running, no shutdown needed", jobName);
        }
    }

    @Override
    public boolean isRunning() {
        return isRunning.get();
    }

    @Override
    public String getJobName() {
        return jobName;
    }

    @Override
    public StreamExecutionEnvironment getExecutionEnvironment() {
        return executionEnvironmentRef.get();
    }

    @PreDestroy
    public void cleanup() {
        log.info("PreDestroy cleanup for {}", jobName);
        try {
            shutdown(shutdownTimeoutMs);
        } catch (Exception e) {
            log.error("Error during PreDestroy cleanup", e);
        }
    }

    private static class DeduplicateOrderPaymentMethodProcessFunction extends KeyedProcessFunction<String, OrderPaymentMethod, OrderPaymentMethod> {
        private transient ValueState<Boolean> seen;

        @Override
        public void open(Configuration parameters) {
            seen = getRuntimeContext().getState(new ValueStateDescriptor<>("seen", Boolean.class));
        }

        @Override
        public void processElement(OrderPaymentMethod value, Context ctx, Collector<OrderPaymentMethod> out) throws Exception {
            if (seen.value() == null) {
                seen.update(true);
                out.collect(value);
            }
        }
    }

    public static class PaymentMethodAggregationMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderPaymentMethodAggregation, MessageRequest<OrderPaymentMethodAggregation>>, java.io.Serializable {
        private final String eventType;
        private final String topic;

        public PaymentMethodAggregationMessageMapper(String eventType, String topic) {
            this.eventType = eventType;
            this.topic = topic;
        }

        @Override
        public MessageRequest<OrderPaymentMethodAggregation> map(OrderPaymentMethodAggregation agg) {
            Message<OrderPaymentMethodAggregation> message = Message.<OrderPaymentMethodAggregation>builder()
                    .data(agg)
                    .build();
            return MessageRequest.<OrderPaymentMethodAggregation>builder()
                    .type(eventType)
                    .message(message)
                    .key(agg.getPaymentMethod())
                    .topic(topic)
                    .build();
        }
    }
} 