package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.richwindowfunctions;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPincode;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPincodeAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.util.Collector;

public class OrderRefundPincodeWindowFunction implements WindowFunction<OrderRefundPincode, OrderRefundPincodeAggregation, String, TimeWindow> {
    @Override
    public void apply(String pincode, TimeWindow window, Iterable<OrderRefundPincode> input, Collector<OrderRefundPincodeAggregation> out) {
        double totalAmount = 0;
        long totalRefundCount = 0;
        for (OrderRefundPincode event : input) {
            totalAmount += event.getAmount();
            totalRefundCount++;
        }
        out.collect(OrderRefundPincodeAggregation.builder()
            .pincode(pincode)
            .totalAmount(totalAmount)
            .totalRefundCount(totalRefundCount)
            .windowStart(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getStart()).atZone(java.time.ZoneId.systemDefault())))
            .windowEnd(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getEnd()).atZone(java.time.ZoneId.systemDefault())))
            .build());
    }
} 