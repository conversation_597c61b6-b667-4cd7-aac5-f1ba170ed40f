package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPincodeAggregation implements Serializable {
    private String pincode;
    private double totalAmount;
    private long totalOrderCount;
    private String windowStart;
    private String windowEnd;
} 