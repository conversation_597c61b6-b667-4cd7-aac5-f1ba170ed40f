package com.lenskart.platform.fraudcontrol.fraudlensjobs.config;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.utils.ConsulPropertyLoader;
import org.apache.flink.shaded.netty4.io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Properties;
import java.util.stream.Collectors;

import static com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants.*;

/**
 * Created by <PERSON><PERSON><PERSON> <PERSON>
 */
@Configuration
public class ConsulConfig implements EnvironmentAware {

    private static final Logger log = LoggerFactory.getLogger(ConsulConfig.class);

    private Environment env;

    @Override
    public void setEnvironment(Environment environment) {
        this.env = environment;
    }

    @PostConstruct
    public void loadConsulProperties() {
        boolean enabled = Boolean.parseBoolean(env.getProperty(CONSUL_ENABLED, TRUE));
        if (!enabled) {
            log.info("Consul property loading is disabled");
            return;
        }

        String host = env.getProperty(CONSUL_HOST, LOCALHOST);
        int port = Integer.parseInt(env.getProperty(CONSUL_PORT, CONSUL_DEFAULT_PORT));
        String prefix = env.getProperty(CONSUL_KEY_PREFIX, StringUtil.EMPTY_STRING);
        String aclToken = env.getProperty(CONSUL_ACL_TOKEN, StringUtil.EMPTY_STRING);

        log.info("Loading Consul properties from {}:{} with prefix '{}'", host, port, prefix);
        try {
            Properties consulProps = ConsulPropertyLoader.load(host, port,aclToken, prefix);
            MutablePropertySources sources = ((ConfigurableEnvironment) env).getPropertySources();
            sources.addFirst(new PropertiesPropertySource(CONSUL_PROPERTIES, consulProps));
            log.info("Loaded {} properties from Consul", consulProps.size());

            // Print the final resolved value for each property key
            for (String propertyName : ((ConfigurableEnvironment) env).getPropertySources().stream()
                    .filter(ps -> ps instanceof PropertiesPropertySource)
                    .flatMap(ps -> Arrays.stream(((PropertiesPropertySource) ps).getPropertyNames()))
                    .collect(Collectors.toSet())) {
                if(log.isDebugEnabled()){
                    log.debug("Final value: {} = {}", propertyName, env.getProperty(propertyName));
                }
            }


        } catch (Exception e) {
            log.error("Failed to load properties from Consul", e);
            throw e ;
        }
    }
}