package com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
public interface FlinkJob {

    /**
     * Launch the Flink job
     */
    void launch();

    /**
     * Gracefully shutdown the job
     * @param timeoutMs maximum time to wait for shutdown in milliseconds
     * @throws Exception if shutdown fails
     */
    void shutdown(long timeoutMs) throws Exception;

    /**
     * Check if the job is currently running
     * @return true if job is running, false otherwise
     */
    boolean isRunning();

    /**
     * Get the job name
     * @return the job name
     */
    String getJobName();

    /**
     * Get the StreamExecutionEnvironment for this job
     * @return the StreamExecutionEnvironment instance
     */
    StreamExecutionEnvironment getExecutionEnvironment();
}
