package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderValueAggregation implements Serializable {

    private long customerId;
    private double totalAmount;
    private long totalOrderCount;
    private String windowStart;
    private String windowEnd;
}
