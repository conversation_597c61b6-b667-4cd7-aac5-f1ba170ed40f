package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Payment method information for order events
 * 
 * Created by <PERSON><PERSON><PERSON>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentMethod implements Serializable {

    private String method;
    private double amount;

} 