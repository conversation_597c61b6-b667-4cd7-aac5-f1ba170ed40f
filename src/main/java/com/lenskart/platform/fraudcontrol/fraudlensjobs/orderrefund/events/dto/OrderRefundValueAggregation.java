package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderRefundValueAggregation implements Serializable {
    private long customerId;
    private double totalRefundAmount;
    private long totalRefundCount;
    private String windowStart;
    private String windowEnd;
} 