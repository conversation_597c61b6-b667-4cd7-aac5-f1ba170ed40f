package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPaymentMethod implements Serializable {

    private long orderId;
    private long customerId;
    private String paymentMethod;

    private double amount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "UTC")
    private Date createdAt;

}
