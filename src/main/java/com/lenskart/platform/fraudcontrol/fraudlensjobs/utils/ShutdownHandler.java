package com.lenskart.platform.fraudcontrol.fraudlensjobs.utils;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * Comprehensive shutdown handler for managing graceful shutdown of Flink jobs
 * Created by Neeraj Kumar
 */
public class ShutdownHandler {

    private static final Logger log = LoggerFactory.getLogger(ShutdownHandler.class);
    private static final AtomicBoolean shutdownRequested = new AtomicBoolean(false);
    private static final CountDownLatch shutdownLatch = new CountDownLatch(1);
    
    private final List<FlinkJob> jobs;
    private final long shutdownTimeoutMs;
    private final Consumer<Void> preShutdownCallback;
    private final Consumer<Void> postShutdownCallback;

    private ShutdownHandler(Builder builder) {
        this.jobs = builder.jobs;
        this.shutdownTimeoutMs = builder.shutdownTimeoutMs;
        this.preShutdownCallback = builder.preShutdownCallback;
        this.postShutdownCallback = builder.postShutdownCallback;
    }

    /**
     * Initialize the shutdown handler and register shutdown hooks
     */
    public void initialize() {
        log.info("Initializing shutdown handler for {} jobs with timeout {}ms", jobs.size(), shutdownTimeoutMs);
        setupShutdownHooks();
        setupSignalHandlers();
    }

    /**
     * Wait for shutdown signal
     * @throws InterruptedException if interrupted while waiting
     */
    public void waitForShutdown() throws InterruptedException {
        log.info("Waiting for shutdown signal...");
        shutdownLatch.await();
    }

    /**
     * Request shutdown programmatically
     */
    public void requestShutdown() {
        if (shutdownRequested.compareAndSet(false, true)) {
            log.info("Shutdown requested programmatically");
            shutdownLatch.countDown();
        }
    }

    /**
     * Check if shutdown has been requested
     * @return true if shutdown has been requested
     */
    public boolean isShutdownRequested() {
        return shutdownRequested.get();
    }

    /**
     * Perform graceful shutdown
     */
    public void performShutdown() {
        log.info("Performing graceful shutdown");
        
        try {
            // Execute pre-shutdown callback
            if (preShutdownCallback != null) {
                log.info("Executing pre-shutdown callback");
                preShutdownCallback.accept(null);
            }

            // Shutdown all jobs
            boolean success = JobLifecycleManager.waitForShutdown(jobs, shutdownTimeoutMs);
            
            if (success) {
                log.info("All jobs shutdown successfully");
            } else {
                log.warn("Some jobs did not shutdown within timeout");
            }

            // Execute post-shutdown callback
            if (postShutdownCallback != null) {
                log.info("Executing post-shutdown callback");
                postShutdownCallback.accept(null);
            }

            log.info("Graceful shutdown completed");
            
        } catch (Exception e) {
            log.error("Error during shutdown", e);
        }
    }

    private void setupShutdownHooks() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("JVM shutdown hook triggered");
            requestShutdown();
        }, "JVM-ShutdownHook"));
    }

    private void setupSignalHandlers() {
        // Handle SIGTERM
        try {
            sun.misc.Signal.handle(new sun.misc.Signal("TERM"), signal -> {
                log.info("Received SIGTERM signal");
                requestShutdown();
            });
        } catch (Exception e) {
            log.warn("Could not register SIGTERM handler", e);
        }

        // Handle SIGINT (Ctrl+C)
        try {
            sun.misc.Signal.handle(new sun.misc.Signal("INT"), signal -> {
                log.info("Received SIGINT signal");
                requestShutdown();
            });
        } catch (Exception e) {
            log.warn("Could not register SIGINT handler", e);
        }
    }

    /**
     * Builder for creating ShutdownHandler instances
     */
    public static class Builder {
        private List<FlinkJob> jobs;
        private long shutdownTimeoutMs = 30000; // Default 30 seconds
        private Consumer<Void> preShutdownCallback;
        private Consumer<Void> postShutdownCallback;

        public Builder jobs(List<FlinkJob> jobs) {
            this.jobs = jobs;
            return this;
        }

        public Builder shutdownTimeoutMs(long shutdownTimeoutMs) {
            this.shutdownTimeoutMs = shutdownTimeoutMs;
            return this;
        }

        public Builder preShutdownCallback(Consumer<Void> callback) {
            this.preShutdownCallback = callback;
            return this;
        }

        public Builder postShutdownCallback(Consumer<Void> callback) {
            this.postShutdownCallback = callback;
            return this;
        }

        public ShutdownHandler build() {
            if (jobs == null) {
                throw new IllegalStateException("Jobs list must be provided");
            }
            return new ShutdownHandler(this);
        }
    }

    /**
     * Create a new builder instance
     * @return new Builder instance
     */
    public static Builder builder() {
        return new Builder();
    }
} 