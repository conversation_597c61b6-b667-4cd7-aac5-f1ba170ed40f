package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.richwindowfunctions;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPaymentMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPaymentMethodAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.util.Collector;

public class OrderRefundPaymentMethodWindowFunction implements WindowFunction<OrderRefundPaymentMethod, OrderRefundPaymentMethodAggregation, String, TimeWindow> {
    @Override
    public void apply(String paymentMethod, TimeWindow window, Iterable<OrderRefundPaymentMethod> input, Collector<OrderRefundPaymentMethodAggregation> out) {
        double totalAmount = 0;
        long totalRefundCount = 0;
        for (OrderRefundPaymentMethod event : input) {
            totalAmount += event.getAmount();
            totalRefundCount++;
        }
        out.collect(OrderRefundPaymentMethodAggregation.builder()
            .paymentMethod(paymentMethod)
            .totalAmount(totalAmount)
            .totalRefundCount(totalRefundCount)
            .windowStart(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getStart()).atZone(java.time.ZoneId.systemDefault())))
            .windowEnd(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getEnd()).atZone(java.time.ZoneId.systemDefault())))
            .build());
    }
} 