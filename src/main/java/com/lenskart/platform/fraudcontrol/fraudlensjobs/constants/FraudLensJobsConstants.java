package com.lenskart.platform.fraudcontrol.fraudlensjobs.constants;

import lombok.experimental.UtilityClass;

import java.time.format.DateTimeFormatter;

/**
 * Created by <PERSON><PERSON><PERSON>
 */

@UtilityClass
public class FraudLensJobsConstants {

    //Consul
    public static final String CONSUL_ENABLED = "consul.enabled";
    public static final String CONSUL_HOST = "consul.host";
    public static final String CONSUL_PORT = "consul.port";
    public static final String CONSUL_KEY_PREFIX = "consul.keyPrefix";
    public static final String CONSUL_PROPERTIES = "consulProperties";
    public static final String LOCALHOST = "localhost";
    public static final String TRUE = "true";
    public static final String CONSUL_DEFAULT_PORT = "8500";
    public static final String CONSUL_ACL_TOKEN = "consul.aclToken";

    //Job type
    public static final String ORDER = "ORDER";
    public static final String ORDER_REFUND = "ORDER_REFUND";

    //Flink
    public static final String ROCKSDB = "rocksdb";


    //Events
    public static final String EVENT_ORDER_PAYMENT_METHOD = "order_payment_method";
    public static final String EVENT_ORDER_PINCODE= "order_pincode";
    public static final String EVENT_ORDER_USER_TOTAL_VALUE = "user_order_total_value";
    public static final String EVENT_ORDER_PINCODE_SHORT_DURATION = "pincode_order_count_short_duration";
    public static final String EVENT_ORDER_PINCODE_LONG_DURATION = "pincode_order_count_long_duration";
    public static final String EVENT_ORDER_PAYMENT_METHOD_SHORT_DURATION = "payment_method_count_short_duration";
    public static final String EVENT_ORDER_PAYMENT_METHOD_LONG_DURATION = "payment_method_count_long_duration";
    public static final String EVENT_ORDER_REFUND_PAYMENT_METHOD = "order_refund_payment_method";
    public static final String EVENT_ORDER_REFUND_PINCODE = "order_refund_pincode";
    public static final String EVENT_ORDER_REFUND_USER_TOTAL_VALUE = "user_order_refund_total_value";
    public static final String EVENT_ORDER_REFUND_PINCODE_SHORT_DURATION = "refund_pincode_count_short_duration";
    public static final String EVENT_ORDER_REFUND_PINCODE_LONG_DURATION = "refund_pincode_count_long_duration";
    public static final String EVENT_ORDER_REFUND_PAYMENT_METHOD_SHORT_DURATION = "refund_payment_method_count_short_duration";
    public static final String EVENT_ORDER_REFUND_PAYMENT_METHOD_LONG_DURATION = "refund_payment_method_count_long_duration";

    public static final DateTimeFormatter WINDOW_FORMATTER =
        DateTimeFormatter.ofPattern("dd:MM:yyyy-HH:mm:ss z");
    public static final String ORDER_PAYMENT_METHOD_AGGREGATION = "ORDER_PAYMENT_METHOD_AGGREGATION";
    public static final String ORDER_PINCODE_AGGREGATION = "ORDER_PINCODE_AGGREGATION";
    public static final String ORDER_REFUND_PAYMENT_METHOD_AGGREGATION = "ORDER_REFUND_PAYMENT_METHOD_AGGREGATION";
    public static final String ORDER_REFUND_PINCODE_AGGREGATION = "ORDER_REFUND_PINCODE_AGGREGATION";
    public static final String UNDER_SCORE = "_";
}
