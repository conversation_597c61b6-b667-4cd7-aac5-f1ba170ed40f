package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderRefundPincodeAggregation implements Serializable {
    private String pincode;
    private double totalAmount;
    private long totalRefundCount;
    private String windowStart;
    private String windowEnd;
} 