package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderEvent implements Serializable {

    private long customerId;
    private double amount;
    private String pincode;
    private List<PaymentMethod> paymentMethods;
    private long orderId;
    private Date createdAt;
    private Boolean isPosOrder;
    private String storeId;
    private String country;
    private List<Long> productId;
    private String channel;
    private String ip;
    private Boolean isBrandedFlow;
    private String clientOrg;

}
