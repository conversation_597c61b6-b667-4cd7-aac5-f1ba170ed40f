package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto;

/**
 * DTO representing a refund method with its associated amount.
 * 
 * <AUTHOR>
 */
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundMethod implements Serializable {

    private String method;
    private double amount;
} 