package com.lenskart.platform.fraudcontrol.fraudlensjobs.utils;

import com.ecwid.consul.v1.ConsulClient;
import com.ecwid.consul.v1.Response;
import com.ecwid.consul.v1.kv.model.GetValue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;
import java.util.List;
import java.util.Properties;

/**
 * Created by <PERSON>eera<PERSON> Kumar
 */
public class ConsulPropertyLoader {

    private static final Logger log = LoggerFactory.getLogger(ConsulPropertyLoader.class);

    public static Properties load(String host, int port,String aclToken, String prefix) {
        log.info("Connecting to <PERSON> at {}:{} with prefix '{}'", host, port, prefix);
        ConsulClient client = new ConsulClient(host, port);
        Response<List<GetValue>> response = client.getKVValues(prefix, aclToken);
        Properties props = new Properties();

        if (response != null && response.getValue() != null) {
            for (GetValue kv : response.getValue()) {
                try {
                    String key = kv.getKey().replace(prefix + "/", "");
                    String value = new String(Base64.getDecoder().decode(kv.getValue()));
                    try (java.io.StringReader reader = new java.io.StringReader(value)) {
                        props.load(reader);
                    }
                    log.debug("Loaded Consul property: {}=****", key);
                } catch (Exception e) {
                    log.error("Failed to decode Consul property for key: {}", kv.getKey(), e);
                }
            }
        } else {
            log.warn("No properties found in Consul for prefix '{}'", prefix);
        }

        return props;
    }
}