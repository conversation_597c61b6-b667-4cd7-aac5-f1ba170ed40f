package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.jobs.impl;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.deserializer.MessageRequestDeserializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.Message;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.MessageRequest;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderEvent;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderPaymentMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderPincode;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderValueAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.PaymentMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.serializer.MessageRequestSerializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.richwindowfunctions.UserValueWindowFunction;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.windowing.assigners.SlidingProcessingTimeWindows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.configuration.StateBackendOptions;
import org.apache.flink.configuration.CheckpointingOptions;
import org.apache.flink.streaming.api.windowing.time.Time;

import javax.annotation.PreDestroy;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import java.nio.charset.StandardCharsets;


/**
 * Flink job for aggregating order events for fraud detection.
 *
 * <p>This job consumes order events from Kafka, deduplicates them by orderId, and then:
 * <ul>
 *   <li>Aggregates total order value and count per user in a sliding window, outputting to a Kafka topic.</li>
 *   <li>Forwards each order event by pincode to a Kafka topic (no aggregation).</li>
 *   <li>Forwards each order event by payment method to a Kafka topic (no aggregation).</li>
 * </ul>
 *
 * <p>All lambdas and anonymous classes in the Flink pipeline are static or use only final local variables to ensure serialization safety.
 *
 * Created by Neeraj Kumar
 */
@Component(FraudLensJobsConstants.ORDER)
public class OrderEventAggregationJob implements FlinkJob {

    private static final Logger log = LoggerFactory.getLogger(OrderEventAggregationJob.class);

    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    private final AtomicReference<StreamExecutionEnvironment> executionEnvironmentRef = new AtomicReference<>();

    private final AtomicReference<CompletableFuture<Void>> jobExecutionFuture = new AtomicReference<>();

    private final ExecutorService executorService = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "OrderEventAggregationJob-Executor");
        t.setDaemon(false);
        return t;
    });

    private final String jobName = "Order Event Aggregation with Multiple Sliding Windows";

    @Value("${order.user.aggregation.window.size.minutes}")
    public int orderUserAggregationWindowSize;

    @Value("${order.user.aggregation.window.slide.minutes}")
    public int orderUserAggregationWindowSlideSize;

    @Value("${kafka.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.order-events-topic}")
    public String orderKafkaOrderEventsTopic;

    @Value("${kafka.order-user-aggregates-topic}")
    public String orderKafkaOrderUserAggregatesTopic;

    @Value("${kafka.order-pincode-topic}")
    public String orderKafkaOrderPincodeTopic;

    @Value("${kafka.order-paymentmethod-topic}")
    public String orderKafkaOrderPaymentMethodTopic;

    @Value("${kafka.order-aggregator-group-id}")
    public String kafkaOrderAggregatorGroupId;

    @Value("${order.event.aggregation.source.name}")
    public String orderEventAggregationSourceName;

    @Value("${flink.checkpoint.interval}")
    public long checkpointInterval;

    @Value("${flink.state.backend}")
    public String flinkStateBackend;

    @Value("${flink.state.checkpoints.dir}")
    public String flinkCheckpointsDir;

    @Value("${flink.order.aggregation.user.parallelism}")
    public int userAggregationParallelism;

    @Value("${flink.order.aggregation.pincode.parallelism}")
    public int pincodeAggregationParallelism;

    @Value("${flink.order.aggregation.paymentmethod.parallelism}")
    public int paymentMethodAggregationParallelism;

    @Value("${flink.shutdown.timeout.ms:30000}")
    public long shutdownTimeoutMs;

    /**
     * Extracts the OrderEvent from a MessageRequest. Returns null if not present.
     * Static for serialization safety in Flink pipelines.
     */
    private static OrderEvent extractOrderEvent(MessageRequest<OrderEvent> value) {
        if (value != null && value.getMessage() != null) {
            return value.getMessage().getData();
        }
        return null;
    }

    /**
     * Builds a MessageRequest for user aggregation results.
     * @param agg Aggregation result
     * @param topic Kafka topic
     * @return MessageRequest for Kafka
     */
    private static MessageRequest<OrderValueAggregation> toUserAggregationMessage(OrderValueAggregation agg, String topic) {
        Message<OrderValueAggregation> message = Message.<OrderValueAggregation>builder()
            .data(agg)
            .build();
        return MessageRequest.<OrderValueAggregation>builder()
            .type(FraudLensJobsConstants.EVENT_ORDER_USER_TOTAL_VALUE)
            .message(message)
            .key(String.valueOf(agg.getCustomerId()))
            .topic(topic)
            .build();
    }

    /**
     * Builds a MessageRequest for pincode events.
     * @param orderEvent Order event
     * @param topic Kafka topic
     * @return MessageRequest for Kafka
     */
    private static MessageRequest<OrderPincode> toOrderPincodeMessage(OrderEvent orderEvent, String topic) {
        OrderPincode agg = OrderPincode.builder()
            .pincode(orderEvent.getPincode())
            .orderId(orderEvent.getOrderId())
            .customerId(orderEvent.getCustomerId())
            .amount(orderEvent.getAmount())
            .createdAt(orderEvent.getCreatedAt())
            .build();
        Message<OrderPincode> msg = Message.<OrderPincode>builder()
            .data(agg)
            .build();
        return MessageRequest.<OrderPincode>builder()
            .message(msg)
            .type(FraudLensJobsConstants.EVENT_ORDER_PINCODE)
            .key(agg.getPincode() != null ? agg.getPincode() : null)
            .topic(topic)
            .build();
    }

    /**
     * Builds a MessageRequest for payment method events.
     * @param orderEvent Order event
     * @param topic Kafka topic
     * @return MessageRequest for Kafka
     */


    /**
     * Deduplicates OrderEvents by orderId. Only the first event for each orderId is forwarded.
     * Implemented as a static inner class for serialization safety.
     */
    private static class DeduplicateOrderEventProcessFunction extends KeyedProcessFunction<Long, OrderEvent, OrderEvent> {
        private transient ValueState<Boolean> seen;

        @Override
        public void open(Configuration parameters) {
            seen = getRuntimeContext().getState(new ValueStateDescriptor<>("seen", Boolean.class));
        }

        @Override
        public void processElement(OrderEvent value, Context ctx, Collector<OrderEvent> out) throws Exception {
            if (seen.value() == null) {
                seen.update(true);
                out.collect(value); // first time seeing this orderId
            }
            // else: duplicate, do nothing
        }
    }

    /**
     * Launches the Flink job asynchronously. Sets up a shutdown hook for graceful termination.
     */
    @Override
    public void launch() {
        log.info("Starting OrderEventAggregationJob");
        log.info("Config: userWindowSize={} userWindowSlide={} kafkaBootstrapServers={} orderKafkaOrderEventsTopic={} kafkaOrderPaymentMethodAggregatorGroupId={} sourceName={} checkpointInterval={}",
                orderUserAggregationWindowSize, orderUserAggregationWindowSlideSize,
                kafkaBootstrapServers, orderKafkaOrderEventsTopic,
                kafkaOrderAggregatorGroupId, orderEventAggregationSourceName, checkpointInterval);

        if (isRunning.compareAndSet(false, true)) {
            try {
                setupShutdownHook();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        executeJob();
                    } catch (Exception e) {
                        log.error("Job execution failed", e);
                        throw new RuntimeException(e);
                    }
                }, executorService);

                jobExecutionFuture.set(future);
                log.info("OrderEventAggregationJob execution started");
            } catch (Exception e) {
                isRunning.set(false);
                throw e;
            }
        } else {
            log.warn("Job is already running");
        }
    }

    /**
     * Main Flink pipeline logic. Sets up sources, deduplication, aggregations, and sinks.
     *
     * @throws Exception if the job fails to execute
     */
    private void executeJob() throws Exception {
        log.info("executeJob:Config: userWindowSize={} userWindowSlide={} kafkaBootstrapServers={} orderKafkaOrderEventsTopic={} kafkaOrderPaymentMethodAggregatorGroupId={} sourceName={} checkpointInterval={}",
                orderUserAggregationWindowSize, orderUserAggregationWindowSlideSize,
                kafkaBootstrapServers, orderKafkaOrderEventsTopic,
                kafkaOrderAggregatorGroupId, orderEventAggregationSourceName, checkpointInterval);
        Configuration config = new Configuration();
        config.set(StateBackendOptions.STATE_BACKEND, flinkStateBackend);
        config.set(CheckpointingOptions.CHECKPOINTS_DIRECTORY, flinkCheckpointsDir);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        executionEnvironmentRef.set(env);

        env.enableCheckpointing(checkpointInterval);

        // --- Source: Read order events from Kafka ---
        KafkaSource<MessageRequest<OrderEvent>> kafkaSource = KafkaSource.<MessageRequest<OrderEvent>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics(orderKafkaOrderEventsTopic)
                .setGroupId(kafkaOrderAggregatorGroupId)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new MessageRequestDeserializationSchema<>(OrderEvent.class))
                .build();

        DataStream<OrderEvent> events = env.fromSource(
            kafkaSource,
            WatermarkStrategy.noWatermarks(),
            orderEventAggregationSourceName
        )
        // Extract OrderEvent from MessageRequest (static method for serialization safety)
        .map(OrderEventAggregationJob::extractOrderEvent)
        // Filter out nulls
        .filter(Objects::nonNull);

        // --- Deduplicate events by orderId ---
        DataStream<OrderEvent> dedupedEvents = events
            .keyBy(OrderEvent::getOrderId)
            .process(new DeduplicateOrderEventProcessFunction());

        // --- Kafka sinks for each output branch ---
        KafkaSink<MessageRequest<OrderValueAggregation>> userSink = KafkaSink.<MessageRequest<OrderValueAggregation>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderValueAggregation>>builder()
                                .setTopic(orderKafkaOrderUserAggregatesTopic)
                                // Keyed by customerId
                                .setKeySerializationSchema(msgReq -> {
                                    Long customerId = msgReq.getMessage().getData().getCustomerId();
                                    return customerId != null ? customerId.toString().getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();

        KafkaSink<MessageRequest<OrderPincode>> pincodeSink = KafkaSink.<MessageRequest<OrderPincode>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderPincode>>builder()
                                .setTopic(orderKafkaOrderPincodeTopic)
                                // Keyed by pincode
                                .setKeySerializationSchema(msgReq -> {
                                    String pincode = msgReq.getMessage().getData().getPincode();
                                    return pincode != null ? pincode.getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();

        KafkaSink<MessageRequest<OrderPaymentMethod>> paymentMethodSink = KafkaSink.<MessageRequest<OrderPaymentMethod>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderPaymentMethod>>builder()
                                .setTopic(orderKafkaOrderPaymentMethodTopic)
                                // Keyed by payment method
                                .setKeySerializationSchema(msgReq -> {
                                    String paymentMethod = msgReq.getMessage().getData().getPaymentMethod();
                                    return paymentMethod != null ? paymentMethod.getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();

        // --- Aggregation 1: Total order value and count per user (sliding window) ---
        // Use final local variables for topics to avoid capturing outer class in lambdas
        final String userAggregatesTopic = orderKafkaOrderUserAggregatesTopic;
        final String pincodeTopic = orderKafkaOrderPincodeTopic;
        final String paymentMethodTopic = orderKafkaOrderPaymentMethodTopic;
        dedupedEvents
            .keyBy(OrderEvent::getCustomerId)
            .window(SlidingProcessingTimeWindows.of(
                Time.minutes(orderUserAggregationWindowSize),
                Time.minutes(orderUserAggregationWindowSlideSize)))
            .apply(new UserValueWindowFunction())
            .returns(new TypeHint<OrderValueAggregation>() {})
            // Map aggregation result to Kafka message (static method, local topic variable)
            .map(new UserAggregationMessageMapper(userAggregatesTopic))
            .returns(new TypeHint<MessageRequest<OrderValueAggregation>>() {})
            .sinkTo(userSink)
            .setParallelism(userAggregationParallelism);

        // --- For each order event, create and send a pincode event (no aggregation) ---
        dedupedEvents
            .keyBy(OrderEvent::getPincode)
            // Map to Kafka message (static method, local topic variable)
            .map(new OrderPincodeMessageMapper(pincodeTopic))
            .returns(new TypeHint<MessageRequest<OrderPincode>>() {})
            .sinkTo(pincodeSink)
            .setParallelism(pincodeAggregationParallelism);

        // --- For each order event, create and send payment method events for each payment method with amount > 0 ---
        dedupedEvents
            .flatMap(new OrderPaymentMethodFlatMapper(paymentMethodTopic))
            .returns(new TypeHint<MessageRequest<OrderPaymentMethod>>() {})
            .sinkTo(paymentMethodSink)
            .setParallelism(paymentMethodAggregationParallelism);

        env.execute(jobName);
    }

    private void setupShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutdown hook triggered for {}", jobName);
            try {
                shutdown(shutdownTimeoutMs);
            } catch (Exception e) {
                log.error("Error during shutdown hook execution", e);
            }
        }, "ShutdownHook-" + jobName));
    }

    @Override
    public void shutdown(long timeoutMs) throws Exception {
        log.info("Initiating graceful shutdown for {} with timeout {}ms", jobName, timeoutMs);

        if (isRunning.compareAndSet(true, false)) {
            try {
                // Cancel the job execution if it's running
                CompletableFuture<Void> future = jobExecutionFuture.get();
                if (future != null && !future.isDone()) {
                    log.info("Cancelling job execution");
                    future.cancel(true);
                }

                // Shutdown the executor service
                log.info("Shutting down executor service");
                executorService.shutdown();

                if (!executorService.awaitTermination(timeoutMs, TimeUnit.MILLISECONDS)) {
                    log.warn("Executor service did not terminate within {}ms, forcing shutdown", timeoutMs);
                    executorService.shutdownNow();
                    if (!executorService.awaitTermination(5000, TimeUnit.MILLISECONDS)) {
                        log.error("Executor service did not terminate");
                    }
                }

                // Clear references
                executionEnvironmentRef.set(null);
                jobExecutionFuture.set(null);

                log.info("Graceful shutdown completed for {}", jobName);
            } catch (Exception e) {
                log.error("Error during shutdown", e);
                throw e;
            }
        } else {
            log.info("Job {} is not running, no shutdown needed", jobName);
        }
    }

    @Override
    public boolean isRunning() {
        return isRunning.get();
    }

    @Override
    public String getJobName() {
        return jobName;
    }

    @Override
    public StreamExecutionEnvironment getExecutionEnvironment() {
        return executionEnvironmentRef.get();
    }

    @PreDestroy
    public void cleanup() {
        log.info("PreDestroy cleanup for {}", jobName);
        try {
            shutdown(shutdownTimeoutMs);
        } catch (Exception e) {
            log.error("Error during PreDestroy cleanup", e);
        }
    }

    public static class UserAggregationMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderValueAggregation, MessageRequest<OrderValueAggregation>>, java.io.Serializable {
        private final String topic;

        public UserAggregationMessageMapper(String topic) {
            this.topic = topic;
        }

        @Override
        public MessageRequest<OrderValueAggregation> map(OrderValueAggregation agg) {
            Message<OrderValueAggregation> message = Message.<OrderValueAggregation>builder()
                    .data(agg)
                    .build();
            return MessageRequest.<OrderValueAggregation>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_USER_TOTAL_VALUE)
                    .message(message)
                    .topic(topic)
                    .build();
        }
    }

    public static class OrderPincodeMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderEvent, MessageRequest<OrderPincode>>, java.io.Serializable {
        private final String topic;

        public OrderPincodeMessageMapper(String topic) {
            this.topic = topic;
        }

        @Override
        public MessageRequest<OrderPincode> map(OrderEvent orderEvent) {
            OrderPincode pincode = OrderPincode.builder()
                    .orderId(orderEvent.getOrderId())
                    .customerId(orderEvent.getCustomerId())
                    .pincode(orderEvent.getPincode())
                    .amount(orderEvent.getAmount())
                    .createdAt(new java.util.Date())
                    .build();
            return MessageRequest.<OrderPincode>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_PINCODE)
                    .message(Message.<OrderPincode>builder().data(pincode).build())
                    .key(pincode.getPincode())
                    .topic(topic)
                    .build();
        }
    }

    public static class OrderPaymentMethodFlatMapper implements org.apache.flink.api.common.functions.FlatMapFunction<OrderEvent, MessageRequest<OrderPaymentMethod>>, java.io.Serializable {
        private final String topic;

        public OrderPaymentMethodFlatMapper(String topic) {
            this.topic = topic;
        }

        @Override
        public void flatMap(OrderEvent orderEvent, org.apache.flink.util.Collector<MessageRequest<OrderPaymentMethod>> out) throws Exception {
            // Create events for each payment method with amount > 0
            if (orderEvent.getPaymentMethods() != null) {
                for (PaymentMethod paymentMethod : orderEvent.getPaymentMethods()) {
                    if (paymentMethod.getAmount() > 0) {
                        OrderPaymentMethod orderPaymentMethod = OrderPaymentMethod.builder()
                                .orderId(orderEvent.getOrderId())
                                .customerId(orderEvent.getCustomerId())
                                .paymentMethod(paymentMethod.getMethod())
                                .amount(paymentMethod.getAmount())
                                .createdAt(new java.util.Date())
                                .build();
                        
                        MessageRequest<OrderPaymentMethod> messageRequest = MessageRequest.<OrderPaymentMethod>builder()
                                .type(FraudLensJobsConstants.EVENT_ORDER_PAYMENT_METHOD)
                                .message(Message.<OrderPaymentMethod>builder().data(orderPaymentMethod).build())
                                .key(orderPaymentMethod.getPaymentMethod())
                                .topic(topic)
                                .build();
                        
                        out.collect(messageRequest);
                    }
                }
            }
        }
    }
}