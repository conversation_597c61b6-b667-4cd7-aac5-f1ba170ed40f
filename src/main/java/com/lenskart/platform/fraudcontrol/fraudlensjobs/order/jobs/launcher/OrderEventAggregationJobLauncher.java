package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.jobs.launcher;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.config.SpringConfig;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.utils.ShutdownHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>eera<PERSON> Kumar
 */
public class OrderEventAggregationJobLauncher {

    private static final Logger log = LoggerFactory.getLogger(OrderEventAggregationJobLauncher.class);

    private static AnnotationConfigApplicationContext context;

    private static ShutdownHandler shutdownHandler;

    public static void main(String[] args) throws Exception {
        System.out.println("Starting OrderEventAggregationJobLauncher");
        
        try {
            // Initialize Spring context
            context = new AnnotationConfigApplicationContext(SpringConfig.class);
            log.info("Spring context initialized");
            
            // Get all Flink jobs
            Map<String, FlinkJob> flinkJobs = context.getBeansOfType(FlinkJob.class);
            log.info("Available Flink jobs: {}", flinkJobs.keySet());
            
            // Get the order job
            FlinkJob orderJob = flinkJobs.get(FraudLensJobsConstants.ORDER);
            if (orderJob == null) {
                throw new IllegalStateException("Order job not found in Spring context");
            }
            
            // Create shutdown handler
            shutdownHandler = ShutdownHandler.builder()
                    .jobs(List.of(orderJob))
                    .shutdownTimeoutMs(30000) // 30 seconds
                    .preShutdownCallback(v -> log.info("Pre-shutdown callback executed"))
                    .postShutdownCallback(v -> {
                        log.info("Post-shutdown callback executed");
                        if (context != null) {
                            log.info("Closing Spring context");
                            context.close();
                        }
                    })
                    .build();
            
            // Initialize shutdown handler
            shutdownHandler.initialize();
            
            // Launch the job
            log.info("Launching job: {}", orderJob.getJobName());
            orderJob.launch();
            
            // Wait for shutdown signal
            log.info("Job launched successfully. Waiting for shutdown signal...");
            shutdownHandler.waitForShutdown();
            
        } catch (Exception e) {
            log.error("Error in OrderEventAggregationJobLauncher", e);
            throw e;
        } finally {
            // Perform graceful shutdown
            if (shutdownHandler != null) {
                shutdownHandler.performShutdown();
            }
        }
    }

    /**
     * Utility method to request shutdown programmatically
     */
    public static void requestShutdownProgrammatically() {
        if (shutdownHandler != null) {
            shutdownHandler.requestShutdown();
        }
    }

    /**
     * Utility method to check if shutdown has been requested
     * @return true if shutdown has been requested
     */
    public static boolean isShutdownRequested() {
        return shutdownHandler != null && shutdownHandler.isShutdownRequested();
    }
}
