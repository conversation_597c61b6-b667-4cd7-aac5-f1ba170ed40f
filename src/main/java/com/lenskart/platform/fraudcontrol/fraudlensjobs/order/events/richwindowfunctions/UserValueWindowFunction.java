package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.richwindowfunctions;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderEvent;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderValueAggregation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.RichWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.stream.StreamSupport;

import static com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants.WINDOW_FORMATTER;


/**
 * Created by <PERSON><PERSON>j Kumar
 */
public class UserValueWindowFunction extends RichWindowFunction<OrderEvent, OrderValueAggregation, Long, TimeWindow> {

    private static final Logger log = LoggerFactory.getLogger(UserValueWindowFunction.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        // Initialize resources if needed
    }

    @Override
    public void close() throws Exception {
        log.info("UserValueWindowFunction closed, resources cleaned up.");
    }

    @Override
    public void apply(Long key, TimeWindow window, Iterable<OrderEvent> input, Collector<OrderValueAggregation> out) {
        long count = StreamSupport.stream(input.spliterator(), false).count();
        double totalAmount = StreamSupport.stream(input.spliterator(), false).mapToDouble(OrderEvent::getAmount).sum();
        OrderValueAggregation agg = OrderValueAggregation.builder()
                .customerId(key)
                .totalAmount(totalAmount)
                .totalOrderCount(count)
                .windowStart(WINDOW_FORMATTER.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(window.getStart()), ZoneId.systemDefault())))
                .windowEnd(WINDOW_FORMATTER.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(window.getEnd()), ZoneId.systemDefault())))
                .build();
        out.collect(agg);
    }
} 