package com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.serializer;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.MessageRequest;
import org.apache.flink.api.common.serialization.SerializationSchema;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
public class MessageRequestSerializationSchema<T> implements SerializationSchema<MessageRequest<T>> {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public byte[] serialize(MessageRequest<T> element) {
        try {
            return OBJECT_MAPPER.writeValueAsBytes(element);
        } catch (Exception e) {
            throw new RuntimeException("Serialization failed", e);
        }
    }
}