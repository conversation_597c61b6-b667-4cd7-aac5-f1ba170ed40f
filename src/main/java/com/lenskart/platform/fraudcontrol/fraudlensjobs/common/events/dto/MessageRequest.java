package com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageRequest<T> implements Serializable {

    private String type;
    private Message<T> message;

    @Builder.Default
    private int count = 0;

    private String key;
    private String topic;
    private String partition;
    private String offset;
}
