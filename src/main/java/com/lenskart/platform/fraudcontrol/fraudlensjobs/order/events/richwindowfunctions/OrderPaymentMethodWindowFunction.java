package com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.richwindowfunctions;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderPaymentMethod;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.order.events.dto.OrderPaymentMethodAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.util.Collector;

public class OrderPaymentMethodWindowFunction implements WindowFunction<OrderPaymentMethod, OrderPaymentMethodAggregation, String, TimeWindow> {
    @Override
    public void apply(String paymentMethod, TimeWindow window, Iterable<OrderPaymentMethod> input, Collector<OrderPaymentMethodAggregation> out) {
        double totalAmount = 0;
        long totalOrderCount = 0;
        for (OrderPaymentMethod event : input) {
            totalAmount += event.getAmount();
            totalOrderCount++;
        }
        out.collect(OrderPaymentMethodAggregation.builder()
            .paymentMethod(paymentMethod)
            .totalAmount(totalAmount)
            .totalOrderCount(totalOrderCount)
            .windowStart(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getStart()).atZone(java.time.ZoneId.systemDefault())))
            .windowEnd(FraudLensJobsConstants.WINDOW_FORMATTER.format(java.time.Instant.ofEpochMilli(window.getEnd()).atZone(java.time.ZoneId.systemDefault())))
            .build());
    }
} 