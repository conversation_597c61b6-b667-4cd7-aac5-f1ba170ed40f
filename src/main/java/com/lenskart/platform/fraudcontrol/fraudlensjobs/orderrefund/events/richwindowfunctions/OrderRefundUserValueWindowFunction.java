package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.richwindowfunctions;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundEvent;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundValueAggregation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.RichWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.stream.StreamSupport;

import static com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants.WINDOW_FORMATTER;

public class OrderRefundUserValueWindowFunction extends RichWindowFunction<OrderRefundEvent, OrderRefundValueAggregation, Long, TimeWindow> {
    private static final Logger log = LoggerFactory.getLogger(OrderRefundUserValueWindowFunction.class);

    @Override
    public void open(Configuration parameters) throws Exception {
        // Initialize resources if needed
    }

    @Override
    public void close() throws Exception {
        log.info("OrderRefundUserValueWindowFunction closed, resources cleaned up.");
    }

    @Override
    public void apply(Long key, TimeWindow window, Iterable<OrderRefundEvent> input, Collector<OrderRefundValueAggregation> out) {
        long count = StreamSupport.stream(input.spliterator(), false).count();
        double totalRefundAmount = StreamSupport.stream(input.spliterator(), false).mapToDouble(OrderRefundEvent::getAmount).sum();
        OrderRefundValueAggregation agg = OrderRefundValueAggregation.builder()
                .customerId(key)
                .totalRefundAmount(totalRefundAmount)
                .totalRefundCount(count)
                .windowStart(WINDOW_FORMATTER.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(window.getStart()), ZoneId.systemDefault())))
                .windowEnd(WINDOW_FORMATTER.format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(window.getEnd()), ZoneId.systemDefault())))
                .build();
        out.collect(agg);
    }
} 