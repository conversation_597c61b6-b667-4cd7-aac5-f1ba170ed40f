package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto;

/**
 * DTO representing an order refund event with all associated details.
 * 
 * <AUTHOR>
 */
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderRefundEvent implements Serializable {

    private long refundId;
    private long customerId;
    private String pincode;
    private String storeId;
    private String country;
    private List<Long> productId;
    private double amount;
    private String refundSource;
    private String refundReason;
    private String ip;
    private String clientOrg;
    private List<RefundMethod> refundMethods;
} 