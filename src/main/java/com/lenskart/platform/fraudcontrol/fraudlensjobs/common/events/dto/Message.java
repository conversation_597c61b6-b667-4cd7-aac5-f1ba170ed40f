package com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Message<T> implements Serializable {

    private Object header;
    private Object args;
    private T data;

}
