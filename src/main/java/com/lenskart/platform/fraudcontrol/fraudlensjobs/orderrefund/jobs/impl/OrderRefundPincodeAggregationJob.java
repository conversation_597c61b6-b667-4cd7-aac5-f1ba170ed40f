package com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.jobs.impl;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.Message;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.constants.FraudLensJobsConstants;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.deserializer.MessageRequestDeserializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.MessageRequest;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPincode;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.dto.OrderRefundPincodeAggregation;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.orderrefund.events.richwindowfunctions.OrderRefundPincodeWindowFunction;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.serializer.MessageRequestSerializationSchema;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.configuration.CheckpointingOptions;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.StateBackendOptions;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.SlidingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Flink job for aggregating order refund events by pincode using sliding windows.
 *
 * <p>This job consumes refund pincode events from Kafka and performs windowed aggregation by pincode, outputting to a Kafka topic.
 *
 * <p>All refund event jobs, DTOs, and configuration are kept separate from order event jobs.
 *
 * Created by Neeraj Kumar
 */
@Component(FraudLensJobsConstants.ORDER_REFUND_PINCODE_AGGREGATION)
public class OrderRefundPincodeAggregationJob implements FlinkJob {
    private static final Logger log = LoggerFactory.getLogger(OrderRefundPincodeAggregationJob.class);

    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicReference<StreamExecutionEnvironment> executionEnvironmentRef = new AtomicReference<>();
    private final AtomicReference<CompletableFuture<Void>> jobExecutionFuture = new AtomicReference<>();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor(r -> {
        Thread t = new Thread(r, "OrderRefundPincodeAggregationJob-Executor");
        t.setDaemon(false);
        return t;
    });
    private final String jobName = "Order Refund Pincode Aggregation with Multiple Sliding Windows";

    @Value("${orderrefund.pincode.aggregation.window.size.short.duration}")
    public int windowSizeShortDuration;

    @Value("${orderrefund.pincode.aggregation.window.slide.short.duration}")
    public int windowSlideShortDuration;

    @Value("${orderrefund.pincode.aggregation.window.size.long.duration}")
    public int windowSizeLongDuration;

    @Value("${orderrefund.pincode.aggregation.window.slide.long.duration}")
    public int windowSlideLongDuration;

    @Value("${kafka.bootstrap-servers}")
    public String kafkaBootstrapServers;

    @Value("${kafka.orderrefund-pincode-topic}")
    public String refundKafkaOrderRefundPincodeTopic;

    @Value("${kafka.orderrefund-pincode-aggregates-hourly-topic}")
    public String refundKafkaOrderRefundPincodeAggregatesHourlyTopic;

    @Value("${kafka.orderrefund.pincode-aggregator-group-id}")
    public String kafkaOrderRefundPincodeAggregatorGroupId;

    @Value("${orderrefund.pincode.aggregation.source.name}")
    public String orderRefundPincodeAggregationSourceName;

    @Value("${flink.checkpoint.interval}")
    public long checkpointInterval;

    @Value("${flink.state.backend}")
    public String flinkStateBackend;

    @Value("${flink.state.checkpoints.dir}")
    public String flinkCheckpointsDir;

    @Value("${flink.orderrefund.aggregation.pincode.parallelism}")
    public int pincodeAggregationParallelism;

    @Value("${flink.shutdown.timeout.ms:30000}")
    public long shutdownTimeoutMs;

    private void executeJob() throws Exception {
        log.info("executeJob:Config: windowSizeShortDuration={} windowSlideShortDuration={} kafkaBootstrapServers={} refundKafkaOrderRefundPincodeTopic={} kafkaOrderRefundPincodeAggregatorGroupId={} sourceName={} checkpointInterval={}",
                windowSizeShortDuration, windowSlideShortDuration,
                kafkaBootstrapServers, refundKafkaOrderRefundPincodeTopic,
                kafkaOrderRefundPincodeAggregatorGroupId, orderRefundPincodeAggregationSourceName, checkpointInterval);
        Configuration config = new Configuration();
        config.set(StateBackendOptions.STATE_BACKEND, flinkStateBackend);
        config.set(CheckpointingOptions.CHECKPOINTS_DIRECTORY, flinkCheckpointsDir);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        executionEnvironmentRef.set(env);
        env.enableCheckpointing(checkpointInterval);

        // --- Source: Read refund pincode events from Kafka ---
        KafkaSource<MessageRequest<OrderRefundPincode>> kafkaSource = KafkaSource.<MessageRequest<OrderRefundPincode>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setTopics(refundKafkaOrderRefundPincodeTopic)
                .setGroupId(kafkaOrderRefundPincodeAggregatorGroupId)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new MessageRequestDeserializationSchema<>(OrderRefundPincode.class))
                .build();

        DataStream<OrderRefundPincode> events = env.fromSource(
            kafkaSource,
            WatermarkStrategy.noWatermarks(),
            orderRefundPincodeAggregationSourceName
        )
        .map(msgReq -> msgReq.getMessage().getData())
        .filter(Objects::nonNull);

        // --- Short duration window aggregation ---
        DataStream<OrderRefundPincodeAggregation> shortDurationAgg = events
            .keyBy(OrderRefundPincode::getPincode)
            .window(SlidingProcessingTimeWindows.of(
                Time.minutes(windowSizeShortDuration),
                Time.minutes(windowSlideShortDuration)))
            .apply(new OrderRefundPincodeWindowFunction())
            .returns(new TypeHint<OrderRefundPincodeAggregation>() {});

        // --- Long duration window aggregation ---
        DataStream<OrderRefundPincodeAggregation> longDurationAgg = events
            .keyBy(OrderRefundPincode::getPincode)
            .window(SlidingProcessingTimeWindows.of(
                Time.minutes(windowSizeLongDuration),
                Time.minutes(windowSlideLongDuration)))
            .apply(new OrderRefundPincodeWindowFunction())
            .returns(new TypeHint<OrderRefundPincodeAggregation>() {});

        // --- Kafka sink for hourly aggregates ---
        KafkaSink<MessageRequest<OrderRefundPincodeAggregation>> pincodeSink = KafkaSink.<MessageRequest<OrderRefundPincodeAggregation>>builder()
                .setBootstrapServers(kafkaBootstrapServers)
                .setRecordSerializer(
                        KafkaRecordSerializationSchema.<MessageRequest<OrderRefundPincodeAggregation>>builder()
                                .setTopic(refundKafkaOrderRefundPincodeAggregatesHourlyTopic)
                                .setKeySerializationSchema(msgReq -> {
                                    String pincode = msgReq.getMessage().getData().getPincode();
                                    return pincode != null ? pincode.getBytes(StandardCharsets.UTF_8) : null;
                                })
                                .setValueSerializationSchema(new MessageRequestSerializationSchema<>())
                                .build()
                )
                .build();

        shortDurationAgg
            .map(new OrderRefundPincodeShortDurationMessageMapper())
            .sinkTo(pincodeSink)
            .setParallelism(pincodeAggregationParallelism);

        longDurationAgg
            .map(new OrderRefundPincodeLongDurationMessageMapper())
            .sinkTo(pincodeSink)
            .setParallelism(pincodeAggregationParallelism);

        env.execute(jobName);
    }

    @Override
    public void launch() {
        isRunning.set(true);
        jobExecutionFuture.set(CompletableFuture.runAsync(() -> {
            try {
                executeJob();
            } catch (Exception e) {
                log.error("Error running job", e);
            }
        }, executorService));
        setupShutdownHook();
    }

    private void setupShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutdown hook triggered for {}", jobName);
            try {
                shutdown(shutdownTimeoutMs);
            } catch (Exception e) {
                log.error("Error during shutdown", e);
            }
        }));
    }

    @Override
    public void shutdown(long timeoutMs) throws Exception {
        isRunning.set(false);
        executorService.shutdown();
        executorService.awaitTermination(timeoutMs, TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean isRunning() {
        return isRunning.get();
    }

    @Override
    public String getJobName() {
        return jobName;
    }

    @Override
    public StreamExecutionEnvironment getExecutionEnvironment() {
        return executionEnvironmentRef.get();
    }

    @PreDestroy
    public void cleanup() {
        try {
            shutdown(shutdownTimeoutMs);
        } catch (Exception e) {
            log.error("Error during cleanup", e);
        }
    }

    public static class OrderRefundPincodeShortDurationMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderRefundPincodeAggregation, MessageRequest<OrderRefundPincodeAggregation>>, java.io.Serializable {
        @Override
        public MessageRequest<OrderRefundPincodeAggregation> map(OrderRefundPincodeAggregation agg) {
            return MessageRequest.<OrderRefundPincodeAggregation>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_PINCODE_SHORT_DURATION)
                    .message(Message.<OrderRefundPincodeAggregation>builder().data(agg).build())
                    .build();
        }
    }

    public static class OrderRefundPincodeLongDurationMessageMapper implements org.apache.flink.api.common.functions.MapFunction<OrderRefundPincodeAggregation, MessageRequest<OrderRefundPincodeAggregation>>, java.io.Serializable {
        @Override
        public MessageRequest<OrderRefundPincodeAggregation> map(OrderRefundPincodeAggregation agg) {
            return MessageRequest.<OrderRefundPincodeAggregation>builder()
                    .type(FraudLensJobsConstants.EVENT_ORDER_REFUND_PINCODE_LONG_DURATION)
                    .message(Message.<OrderRefundPincodeAggregation>builder().data(agg).build())
                    .build();
        }
    }
} 