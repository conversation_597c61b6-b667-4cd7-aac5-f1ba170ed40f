package com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.deserializer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.platform.fraudcontrol.fraudlensjobs.common.events.dto.MessageRequest;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import java.io.IOException;

/**
 * Created by <PERSON><PERSON><PERSON> <PERSON>
 */
public class MessageRequestDeserializationSchema<T> implements DeserializationSchema<MessageRequest<T>> {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final Class<T> clazz;

    public MessageRequestDeserializationSchema(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public MessageRequest<T> deserialize(byte[] message) throws IOException {
        return objectMapper.readValue(message, objectMapper.getTypeFactory().constructParametricType(MessageRequest.class, clazz));
    }

    @Override
    public boolean isEndOfStream(MessageRequest<T> nextElement) {
        return false;
    }

    @Override
    public TypeInformation<MessageRequest<T>> getProducedType() {
        return TypeInformation.of((Class<MessageRequest<T>>) (Class<?>) MessageRequest.class);
    }
}