package com.lenskart.platform.fraudcontrol.fraudlensjobs.utils;

import com.lenskart.platform.fraudcontrol.fraudlensjobs.jobs.FlinkJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Utility class for managing Flink job lifecycle and graceful shutdown operations
 * Created by Neeraj Kumar
 */
public class JobLifecycleManager {

    private static final Logger log = LoggerFactory.getLogger(JobLifecycleManager.class);
    private static final AtomicBoolean shutdownInProgress = new AtomicBoolean(false);

    /**
     * Gracefully shutdown multiple jobs with a timeout
     * @param jobs list of jobs to shut down
     * @param timeoutMs timeout in milliseconds
     * @return CompletableFuture that completes when all jobs are shutdown
     */
    public static CompletableFuture<Void> shutdownJobs(List<FlinkJob> jobs, long timeoutMs) {
        if (shutdownInProgress.compareAndSet(false, true)) {
            log.info("Initiating graceful shutdown for {} jobs with timeout {}ms", jobs.size(), timeoutMs);
            
            CompletableFuture<Void>[] futures = jobs.stream()
                    .filter(FlinkJob::isRunning)
                    .map(job -> CompletableFuture.runAsync(() -> {
                        try {
                            log.info("Shutting down job: {}", job.getJobName());
                            job.shutdown(timeoutMs);
                            log.info("Successfully shutdown job: {}", job.getJobName());
                        } catch (Exception e) {
                            log.error("Error shutting down job: {}", job.getJobName(), e);
                        }
                    }))
                    .toArray(CompletableFuture[]::new);

            return CompletableFuture.allOf(futures)
                    .orTimeout(timeoutMs, TimeUnit.MILLISECONDS)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            log.warn("Some jobs did not shutdown within timeout", throwable);
                        }
                        shutdownInProgress.set(false);
                        log.info("Job shutdown process completed");
                    });
        } else {
            log.warn("Shutdown already in progress");
            return CompletableFuture.completedFuture(null);
        }
    }

    /**
     * Gracefully shutdown a single job with a timeout
     * @param job the job to shutdown
     * @param timeoutMs timeout in milliseconds
     * @return CompletableFuture that completes when the job is shutdown
     */
    public static CompletableFuture<Void> shutdownJob(FlinkJob job, long timeoutMs) {
        return shutdownJobs(List.of(job), timeoutMs);
    }

    /**
     * Check if shutdown is currently in progress
     * @return true if shutdown is in progress
     */
    public static boolean isShutdownInProgress() {
        return shutdownInProgress.get();
    }

    /**
     * Wait for all jobs to complete shutdown
     * @param jobs list of jobs to wait for
     * @param timeoutMs timeout in milliseconds
     * @return true if all jobs shutdown successfully within timeout
     */
    public static boolean waitForShutdown(List<FlinkJob> jobs, long timeoutMs) {
        try {
            shutdownJobs(jobs, timeoutMs).get(timeoutMs, TimeUnit.MILLISECONDS);
            return true;
        } catch (Exception e) {
            log.error("Error waiting for job shutdown", e);
            return false;
        }
    }

    /**
     * Get status summary of all jobs
     * @param jobs list of jobs to check
     * @return status summary string
     */
    public static String getJobStatusSummary(List<FlinkJob> jobs) {
        StringBuilder summary = new StringBuilder("Job Status Summary:\n");
        for (FlinkJob job : jobs) {
            summary.append(String.format("  - %s: %s\n", 
                job.getJobName(), 
                job.isRunning() ? "RUNNING" : "STOPPED"));
        }
        return summary.toString();
    }
} 